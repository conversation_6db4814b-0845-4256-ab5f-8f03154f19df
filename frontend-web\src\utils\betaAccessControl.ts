/**
 * Beta Access Control System
 * Manages module access based on subscription tiers and user roles
 */

export interface User {
  id: string;
  username: string;
  role: string;
  subscriptionTier: string;
  permissions: string[];
  status: string;
}

export interface ModuleAccess {
  id: string;
  name: string;
  requiredTier: 'Beta Access' | 'Professional' | 'Enterprise';
  requiredPermissions: string[];
  isBetaFeature: boolean;
  isPremiumFeature: boolean;
}

// Define module access requirements
export const moduleAccessConfig: Record<string, ModuleAccess> = {
  dashboard: {
    id: 'dashboard',
    name: 'Dashboard',
    requiredTier: 'Beta Access',
    requiredPermissions: ['read'],
    isBetaFeature: false,
    isPremiumFeature: false
  },
  animals: {
    id: 'animals',
    name: 'Animal Management',
    requiredTier: 'Beta Access',
    requiredPermissions: ['read', 'basic_features'],
    isBetaFeature: false,
    isPremiumFeature: false
  },
  health: {
    id: 'health',
    name: 'Health Monitoring',
    requiredTier: 'Beta Access',
    requiredPermissions: ['read', 'basic_features'],
    isBetaFeature: false,
    isPremiumFeature: false
  },
  resources: {
    id: 'resources',
    name: 'Resources & Information',
    requiredTier: 'Beta Access',
    requiredPermissions: ['read'],
    isBetaFeature: false,
    isPremiumFeature: false
  },
  breeding: {
    id: 'breeding',
    name: 'Breeding Management',
    requiredTier: 'Professional',
    requiredPermissions: ['read', 'write', 'advanced_features'],
    isBetaFeature: false,
    isPremiumFeature: true
  },
  financial: {
    id: 'financial',
    name: 'Financial Management',
    requiredTier: 'Professional',
    requiredPermissions: ['read', 'write', 'financial_access'],
    isBetaFeature: false,
    isPremiumFeature: true
  },
  inventory: {
    id: 'inventory',
    name: 'Inventory Management',
    requiredTier: 'Professional',
    requiredPermissions: ['read', 'write', 'inventory_access'],
    isBetaFeature: false,
    isPremiumFeature: true
  },
  analytics: {
    id: 'analytics',
    name: 'AI Analytics',
    requiredTier: 'Enterprise',
    requiredPermissions: ['read', 'write', 'analytics_access'],
    isBetaFeature: false,
    isPremiumFeature: true
  },
  compliance: {
    id: 'compliance',
    name: 'Compliance Tracking',
    requiredTier: 'Enterprise',
    requiredPermissions: ['read', 'write', 'compliance_access'],
    isBetaFeature: false,
    isPremiumFeature: true
  },
  commercial: {
    id: 'commercial',
    name: 'Commercial Operations',
    requiredTier: 'Professional',
    requiredPermissions: ['read', 'write', 'commercial_access'],
    isBetaFeature: false,
    isPremiumFeature: true
  },
  reports: {
    id: 'reports',
    name: 'Advanced Reports',
    requiredTier: 'Professional',
    requiredPermissions: ['read', 'reports_access'],
    isBetaFeature: false,
    isPremiumFeature: true
  },
  feeding: {
    id: 'feeding',
    name: 'Feeding Management',
    requiredTier: 'Professional',
    requiredPermissions: ['read', 'write', 'feeding_access'],
    isBetaFeature: false,
    isPremiumFeature: true
  },
  settings: {
    id: 'settings',
    name: 'Settings & Profile',
    requiredTier: 'Beta Access',
    requiredPermissions: ['read'],
    isBetaFeature: false,
    isPremiumFeature: false
  }
};

// Subscription tier hierarchy
export const subscriptionTiers = {
  'Beta Access': 0,
  'Professional': 1,
  'Enterprise': 2
};

// Check if user has access to a specific module
export const hasModuleAccess = (user: User | null, moduleId: string): boolean => {
  if (!user) return false;

  const moduleConfig = moduleAccessConfig[moduleId];
  if (!moduleConfig) return false;

  // Admin users have access to everything
  if (user.role === 'admin') return true;

  // Check subscription tier
  const userTierLevel = subscriptionTiers[user.subscriptionTier as keyof typeof subscriptionTiers];
  const requiredTierLevel = subscriptionTiers[moduleConfig.requiredTier];

  if (userTierLevel < requiredTierLevel) return false;

  // Check permissions
  const hasRequiredPermissions = moduleConfig.requiredPermissions.every(
    permission => user.permissions.includes(permission) || user.permissions.includes('*')
  );

  return hasRequiredPermissions;
};

// Get accessible modules for a user
export const getAccessibleModules = (user: User | null): string[] => {
  if (!user) return [];

  return Object.keys(moduleAccessConfig).filter(moduleId => 
    hasModuleAccess(user, moduleId)
  );
};

// Get locked modules for a user
export const getLockedModules = (user: User | null): string[] => {
  if (!user) return Object.keys(moduleAccessConfig);

  return Object.keys(moduleAccessConfig).filter(moduleId => 
    !hasModuleAccess(user, moduleId)
  );
};

// Get upgrade message for a locked module
export const getUpgradeMessage = (moduleId: string): string => {
  const moduleConfig = moduleAccessConfig[moduleId];
  if (!moduleConfig) return 'Module not found';

  switch (moduleConfig.requiredTier) {
    case 'Professional':
      return `Upgrade to Professional Plan (R299/month) to access ${moduleConfig.name}`;
    case 'Enterprise':
      return `Upgrade to Enterprise Plan (R599/month) to access ${moduleConfig.name}`;
    default:
      return `Upgrade required to access ${moduleConfig.name}`;
  }
};

// Get subscription plans
export const subscriptionPlans = [
  {
    id: 'beta',
    name: 'Beta Access',
    price: 'Free',
    duration: '30 days',
    features: [
      'Dashboard Overview',
      'Basic Animal Management (50 animals max)',
      'Basic Health Monitoring',
      'Resources & Information Access',
      'Community Support'
    ],
    limitations: [
      'Limited to 50 animals',
      'Basic features only',
      'No advanced analytics',
      'No financial management',
      'No breeding management'
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 'R299',
    duration: 'per month',
    features: [
      'Everything in Beta Access',
      'Unlimited Animals',
      'Advanced Breeding Management',
      'Financial Management',
      'Inventory Management',
      'Commercial Operations',
      'Advanced Reports',
      'Feeding Management',
      'Email Support'
    ],
    limitations: [
      'No AI Analytics',
      'No Compliance Tracking',
      'Limited integrations'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 'R599',
    duration: 'per month',
    features: [
      'Everything in Professional',
      'AI Analytics & Predictions',
      'Compliance Tracking',
      'Advanced Integrations',
      'Custom Reports',
      'Priority Support',
      'Multi-farm Management',
      'API Access',
      'Custom Training'
    ],
    limitations: []
  }
];

// Check if feature is available in current plan
export const isFeatureAvailable = (user: User | null, feature: string): boolean => {
  if (!user) return false;
  
  // Admin users have access to all features
  if (user.role === 'admin') return true;

  // Define feature requirements
  const featureRequirements: Record<string, string> = {
    'unlimited_animals': 'Professional',
    'advanced_breeding': 'Professional',
    'financial_management': 'Professional',
    'inventory_management': 'Professional',
    'ai_analytics': 'Enterprise',
    'compliance_tracking': 'Enterprise',
    'api_access': 'Enterprise',
    'multi_farm': 'Enterprise'
  };

  const requiredTier = featureRequirements[feature];
  if (!requiredTier) return true; // Feature not restricted

  const userTierLevel = subscriptionTiers[user.subscriptionTier as keyof typeof subscriptionTiers];
  const requiredTierLevel = subscriptionTiers[requiredTier as keyof typeof subscriptionTiers];

  return userTierLevel >= requiredTierLevel;
};

// Get beta limitations for display
export const getBetaLimitations = (): string[] => {
  return [
    'Limited to 50 animals maximum',
    'Basic health monitoring only',
    'No financial management',
    'No breeding analytics',
    'No inventory management',
    'No AI insights',
    'Community support only'
  ];
};

// Check if user is in trial period
export const isInTrialPeriod = (user: User | null): boolean => {
  if (!user || user.subscriptionTier !== 'Beta Access') return false;
  
  // This would typically check against trialEndDate from user object
  // For now, assume all beta users are in trial
  return true;
};

// Get trial days remaining (mock implementation)
export const getTrialDaysRemaining = (user: User | null): number => {
  if (!user || user.subscriptionTier !== 'Beta Access') return 0;
  
  // This would typically calculate from trialEndDate
  // For now, return a mock value
  return 27;
};
