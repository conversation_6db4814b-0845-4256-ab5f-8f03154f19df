const express = require('express');
const router = express.Router();
const controller = require('../controllers/mongoDbController');

// Define routes
router.get('/', (req, res) => {
  res.status(200).json({ message: 'Feeding records endpoint' });
});

// Feeding records routes
router.get('/records', controller.getFeedingRecords);
router.get('/inventory', controller.getFeedInventory);

// CRUD operations
router.post('/records', controller.createFeedingRecord);
router.post('/inventory', controller.addFeedInventory);
router.put('/inventory/:id', controller.updateFeedInventory);

module.exports = router;