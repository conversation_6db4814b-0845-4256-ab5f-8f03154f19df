/* Stable Landing Page Styles - Full Screen Fit */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow-x: hidden;
}

.premium-landing {
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #66BB6A 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Navigation */
.premium-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(46, 125, 50, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.premium-nav.scrolled {
  background: rgba(46, 125, 50, 0.98);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.premium-nav-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.premium-logo {
  font-size: 2rem;
  font-weight: 800;
  color: white;
  text-decoration: none;
  background: linear-gradient(45deg, #FFF, #E8F5E8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.premium-nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
}

.premium-nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.premium-nav-link:hover {
  color: #A5D6A7;
}

.premium-nav-actions {
  display: flex;
  gap: 1rem;
}

.premium-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.premium-btn-primary {
  background: linear-gradient(45deg, #FF9800, #F57C00);
  color: white;
}

.premium-btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.premium-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Hero Section */
.asymmetric-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding: 0 2rem;
  background: linear-gradient(135deg, #1B5E20 0%, #2E7D32 50%, #4CAF50 100%);
}

.hero-bg-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(27, 94, 32, 0.8) 0%, rgba(46, 125, 50, 0.6) 100%);
}

.hero-layout {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 2;
  padding-top: 80px;
}

.hero-content-left {
  color: white;
}

.hero-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.title-highlight {
  background: linear-gradient(45deg, #FFC107, #FF9800);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.hero-btn-primary {
  background: linear-gradient(45deg, #FF9800, #F57C00);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.hero-btn-outline {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.hero-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.hero-stats-right {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.hero-stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  color: white;
  transition: all 0.3s ease;
}

.hero-stat-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(-10px);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: 900;
  display: block;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .hero-stats-right {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .hero-stat-card {
    flex: 1;
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .premium-nav-content {
    padding: 1rem;
  }
  
  .premium-nav-links {
    display: none;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .hero-actions {
    justify-content: center;
  }
  
  .hero-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

/* Tabbed Content Section */
.tabbed-content {
  min-height: 60vh;
  background: rgba(255, 255, 255, 0.95);
  padding: 4rem 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.tab-navigation {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  border-bottom: 2px solid #E0E0E0;
}

.tab-btn {
  padding: 1rem 2rem;
  border: none;
  background: transparent;
  color: #666;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-btn.active {
  color: #2E7D32;
  border-bottom-color: #2E7D32;
}

.tab-btn:hover {
  color: #2E7D32;
}

.tab-content {
  min-height: 400px;
}

/* Features Content */
.features-content h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2E7D32;
  margin-bottom: 3rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  color: #2E7D32;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.25rem;
  color: #333;
  margin-bottom: 1rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* Pricing Content */
.pricing-content h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2E7D32;
  margin-bottom: 3rem;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
  transition: transform 0.3s ease;
}

.pricing-card.popular {
  border: 3px solid #FF9800;
  transform: scale(1.05);
}

.pricing-card:hover {
  transform: translateY(-5px);
}

.pricing-card.popular:hover {
  transform: scale(1.05) translateY(-5px);
}

.pricing-card h3 {
  font-size: 1.5rem;
  color: #2E7D32;
  margin-bottom: 1rem;
}

.price {
  font-size: 2.5rem;
  font-weight: 900;
  color: #FF9800;
  margin-bottom: 1rem;
}

.pricing-card p {
  color: #666;
  margin-bottom: 2rem;
}

.pricing-card ul {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.pricing-card li {
  padding: 0.5rem 0;
  color: #333;
}

.pricing-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(45deg, #2E7D32, #4CAF50);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pricing-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
}

/* About Content */
.about-content h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2E7D32;
  margin-bottom: 2rem;
}

.about-content p {
  text-align: center;
  font-size: 1.25rem;
  color: #666;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-item {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* Contact Content */
.contact-content h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2E7D32;
  margin-bottom: 3rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 600px;
  margin: 0 auto;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.contact-item svg {
  color: #2E7D32;
  font-size: 1.5rem;
}

/* Prevent horizontal scroll */
body {
  overflow-x: hidden;
}

.premium-landing * {
  max-width: 100%;
}

/* Responsive Design for Tabs */
@media (max-width: 768px) {
  .tab-navigation {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .tab-btn {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }

  .features-grid,
  .pricing-grid,
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .contact-info {
    gap: 1rem;
  }
}
