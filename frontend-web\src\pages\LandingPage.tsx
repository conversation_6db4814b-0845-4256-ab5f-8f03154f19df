import React, { useState } from 'react';
import {
  Box,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  Chip,
  useTheme,
  alpha,
  Fab
} from '@mui/material';
import {
  Agriculture,
  TrendingUp,
  Security,
  Analytics,
  Pets,
  LocalHospital,
  AccountBalance,
  Inventory,
  Assessment,
  Settings,
  Star,
  CheckCircle,
  ArrowForward,
  PlayArrow,
  Language,
  Login as LoginIcon,
  PersonAdd,
  AutoAwesome,
  Palette,
  Rocket,
  Shield
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { gradientThemes } from '../utils/gradientThemes';
import { useUnifiedTheme } from '../contexts/UnifiedThemeContext';

const LandingPage: React.FC = () => {
  const navigate = useNavigate();
  const { i18n } = useTranslation();
  const { unifiedTheme, themeName, setThemeName } = useUnifiedTheme();
  const [languageAnchor, setLanguageAnchor] = useState<null | HTMLElement>(null);
  const [themeAnchor, setThemeAnchor] = useState<null | HTMLElement>(null);

  const languages = [
    { code: 'en', name: 'English', flag: '🇿🇦' },
    { code: 'af', name: 'Afrikaans', flag: '🇿🇦' },
    { code: 'zu', name: 'isiZulu', flag: '🇿🇦' },
    { code: 'xh', name: 'isiXhosa', flag: '🇿🇦' },
    { code: 'st', name: 'Sesotho', flag: '🇿🇦' },
    { code: 'tn', name: 'Setswana', flag: '🇿🇦' },
    { code: 'ss', name: 'siSwati', flag: '🇿🇦' },
    { code: 've', name: 'Tshivenda', flag: '🇿🇦' },
    { code: 'ts', name: 'Xitsonga', flag: '🇿🇦' },
    { code: 'nr', name: 'isiNdebele', flag: '🇿🇦' },
    { code: 'nso', name: 'Sepedi', flag: '🇿🇦' }
  ];

  // Use unified theme instead of gradientThemes
  const currentTheme = unifiedTheme;

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
    setLanguageAnchor(null);
  };

  const handleThemeChange = (newThemeName: string) => {
    setThemeName(newThemeName as any);
    setThemeAnchor(null);
  };

  const features = [
    {
      icon: <Pets />,
      title: 'Animal Management',
      description: 'Complete livestock tracking and management system',
      available: true
    },
    {
      icon: <LocalHospital />,
      title: 'Health Monitoring',
      description: 'Advanced health tracking and veterinary management',
      available: true
    },
    {
      icon: <Analytics />,
      title: 'Smart Analytics',
      description: 'AI-powered insights and performance analytics',
      available: false
    },
    {
      icon: <AccountBalance />,
      title: 'Financial Management',
      description: 'Complete financial tracking and reporting',
      available: false
    }
  ];

  const subscriptionPlans = [
    {
      name: 'BETA',
      price: 'Free',
      features: ['Basic Animal Management', 'Health Records', 'Limited Analytics'],
      color: currentTheme.gradients.primary,
      popular: false
    },
    {
      name: 'Professional',
      price: 'R299/month',
      features: ['Full Animal Management', 'Advanced Health Monitoring', 'Complete Analytics', 'Financial Management'],
      color: currentTheme.gradients.secondary,
      popular: true
    },
    {
      name: 'Enterprise',
      price: 'R599/month',
      features: ['Everything in Professional', 'Multi-Farm Management', 'API Access', 'Priority Support'],
      color: currentTheme.gradients.accent,
      popular: false
    }
  ];

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: currentTheme.primary,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          zIndex: 0
        }}
      />

      {/* Navigation */}
      <AppBar 
        position="fixed" 
        elevation={0}
        sx={{
          background: alpha(currentTheme.primaryColor, 0.95),
          backdropFilter: 'blur(20px)',
          borderBottom: `1px solid ${alpha('#fff', 0.1)}`
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            <Box
              sx={{
                width: 60,
                height: 60,
                borderRadius: '50%',
                background: currentTheme.gradients.accent,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 8px 32px rgba(0,0,0,0.3)'
              }}
            >
              <Agriculture sx={{ fontSize: 36, color: currentTheme.colors.primary }} />
            </Box>
            <Box>
              <Typography
                variant="h4"
                fontWeight="bold"
                color="white"
                sx={{
                  fontSize: { xs: '1.8rem', md: '2.2rem' },
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                  letterSpacing: '0.5px'
                }}
              >
                AgriIntel
              </Typography>
              <Typography
                variant="subtitle1"
                color="rgba(255,255,255,0.9)"
                sx={{
                  fontWeight: 500,
                  fontSize: '1rem',
                  textShadow: '0 1px 2px rgba(0,0,0,0.2)'
                }}
              >
                🌾 Smart Livestock Management Platform
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* Language Selector */}
            <IconButton
              onClick={(e) => setLanguageAnchor(e.currentTarget)}
              sx={{ color: 'white' }}
            >
              <Language />
            </IconButton>
            <Menu
              anchorEl={languageAnchor}
              open={Boolean(languageAnchor)}
              onClose={() => setLanguageAnchor(null)}
            >
              {languages.map((lang) => (
                <MenuItem
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                >
                  {lang.flag} {lang.name}
                </MenuItem>
              ))}
            </Menu>

            {/* Theme Selector */}
            <IconButton
              onClick={(e) => setThemeAnchor(e.currentTarget)}
              sx={{ color: 'white' }}
            >
              <Palette />
            </IconButton>
            <Menu
              anchorEl={themeAnchor}
              open={Boolean(themeAnchor)}
              onClose={() => setThemeAnchor(null)}
            >
              {Object.entries(gradientThemes).map(([name, theme]) => (
                <MenuItem
                  key={name}
                  onClick={() => handleThemeChange(name)}
                  sx={{
                    background: themeName === name ? alpha(theme.primaryColor, 0.1) : 'transparent'
                  }}
                >
                  <Box
                    sx={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      background: theme.primaryColor,
                      mr: 2
                    }}
                  />
                  {name.charAt(0).toUpperCase() + name.slice(1)}
                </MenuItem>
              ))}
            </Menu>

            <Button
              variant="outlined"
              startIcon={<LoginIcon />}
              onClick={() => navigate('/login')}
              sx={{
                color: 'white',
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  background: alpha('#fff', 0.1)
                }
              }}
            >
              Login
            </Button>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Hero Section */}
      <Container maxWidth="lg" sx={{ pt: 15, pb: 8, position: 'relative', zIndex: 1 }}>
        <Grid container spacing={6} alignItems="center">
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Box sx={{ mb: 2 }}>
                <Typography
                  variant="h6"
                  sx={{
                    color: currentTheme.colors.accent,
                    fontWeight: 'bold',
                    fontSize: '1.2rem',
                    textTransform: 'uppercase',
                    letterSpacing: '2px',
                    mb: 1
                  }}
                >
                  🌾 Powered by AgriIntel
                </Typography>
              </Box>

              <Typography
                variant="h1"
                sx={{
                  fontSize: { xs: '3rem', md: '4.5rem' },
                  fontWeight: 'bold',
                  color: 'white',
                  mb: 3,
                  lineHeight: 1.1,
                  textShadow: '0 4px 8px rgba(0,0,0,0.3)'
                }}
              >
                Transform Your
                <Box component="span" sx={{
                  color: currentTheme.colors.accent,
                  display: 'block',
                  textShadow: `0 0 20px ${alpha(currentTheme.colors.accent, 0.5)}`
                }}>
                  Livestock Farm
                </Box>
                with AI Intelligence
              </Typography>
              
              <Typography
                variant="h5"
                sx={{
                  color: alpha('#fff', 0.9),
                  mb: 4,
                  lineHeight: 1.6,
                  fontSize: { xs: '1.2rem', md: '1.5rem' },
                  textShadow: '0 2px 4px rgba(0,0,0,0.2)'
                }}
              >
                Join 15,000+ South African farmers who trust{' '}
                <Box component="strong" sx={{ color: currentTheme.colors.accent }}>AgriIntel</Box>{' '}
                for smarter livestock management, AI-powered health monitoring, and increased profitability.
              </Typography>

              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                mb: 4,
                flexWrap: 'wrap'
              }}>
                <Chip
                  label="🏆 #1 Livestock Platform in SA"
                  sx={{
                    background: alpha(currentTheme.colors.accent, 0.2),
                    color: currentTheme.colors.accent,
                    fontWeight: 'bold',
                    border: `1px solid ${alpha(currentTheme.colors.accent, 0.3)}`
                  }}
                />
                <Chip
                  label="🚀 750K+ Animals Tracked"
                  sx={{
                    background: alpha('#fff', 0.1),
                    color: 'white',
                    fontWeight: 'bold',
                    border: `1px solid ${alpha('#fff', 0.2)}`
                  }}
                />
                <Chip
                  label="💰 40% Cost Reduction"
                  sx={{
                    background: alpha(currentTheme.colors.secondary, 0.2),
                    color: currentTheme.colors.secondary,
                    fontWeight: 'bold',
                    border: `1px solid ${alpha(currentTheme.colors.secondary, 0.3)}`
                  }}
                />
              </Box>

              <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Rocket />}
                  onClick={() => navigate('/beta-login')}
                  sx={{
                    background: currentTheme.colors.accent,
                    color: currentTheme.colors.primary,
                    px: 4,
                    py: 2,
                    fontSize: '1.2rem',
                    fontWeight: 'bold',
                    '&:hover': {
                      background: alpha(currentTheme.colors.accent, 0.9)
                    }
                  }}
                >
                  Start Free BETA
                </Button>
                
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<PlayArrow />}
                  sx={{
                    color: 'white',
                    borderColor: 'white',
                    px: 4,
                    py: 2,
                    fontSize: '1.2rem',
                    '&:hover': {
                      borderColor: 'white',
                      background: alpha('#fff', 0.1)
                    }
                  }}
                >
                  Watch Demo
                </Button>
              </Box>
            </motion.div>
          </Grid>

          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Box
                sx={{
                  position: 'relative',
                  height: 400,
                  background: alpha('#fff', 0.1),
                  borderRadius: 4,
                  backdropFilter: 'blur(20px)',
                  border: `1px solid ${alpha('#fff', 0.2)}`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Agriculture sx={{ fontSize: 120, color: alpha('#fff', 0.3) }} />
                <Typography
                  variant="h6"
                  sx={{
                    position: 'absolute',
                    bottom: 20,
                    left: 20,
                    color: 'white'
                  }}
                >
                  Dashboard Preview
                </Typography>
              </Box>
            </motion.div>
          </Grid>
        </Grid>
      </Container>

      {/* Features Section */}
      <Box sx={{ py: 8, background: alpha('#fff', 0.05) }}>
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            textAlign="center"
            sx={{
              color: 'white',
              mb: 6,
              fontSize: { xs: '2.5rem', md: '3.5rem' },
              fontWeight: 'bold'
            }}
          >
            Powerful Features for Modern Farmers
          </Typography>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      background: alpha('#fff', 0.1),
                      backdropFilter: 'blur(20px)',
                      border: `1px solid ${alpha('#fff', 0.2)}`,
                      borderRadius: 3,
                      position: 'relative',
                      overflow: 'hidden',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        transition: 'transform 0.3s ease'
                      }
                    }}
                  >
                    {!feature.available && (
                      <Chip
                        label="Premium"
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 16,
                          right: 16,
                          background: currentTheme.accent,
                          color: currentTheme.primary,
                          fontWeight: 'bold'
                        }}
                      />
                    )}

                    <CardContent sx={{ p: 3, textAlign: 'center' }}>
                      <Box
                        sx={{
                          width: 80,
                          height: 80,
                          borderRadius: '50%',
                          background: feature.available
                            ? currentTheme.accent
                            : alpha('#fff', 0.2),
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mx: 'auto',
                          mb: 3,
                          color: feature.available ? currentTheme.primary : '#fff'
                        }}
                      >
                        {React.cloneElement(feature.icon, { fontSize: 'large' })}
                      </Box>

                      <Typography
                        variant="h6"
                        sx={{ color: 'white', mb: 2, fontWeight: 'bold' }}
                      >
                        {feature.title}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{ color: alpha('#fff', 0.8), lineHeight: 1.6 }}
                      >
                        {feature.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Subscription Plans */}
      <Box sx={{ py: 8 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            textAlign="center"
            sx={{
              color: 'white',
              mb: 6,
              fontSize: { xs: '2.5rem', md: '3.5rem' },
              fontWeight: 'bold'
            }}
          >
            Choose Your Plan
          </Typography>

          <Grid container spacing={4} justifyContent="center">
            {subscriptionPlans.map((plan, index) => (
              <Grid item xs={12} md={4} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      background: plan.popular
                        ? alpha('#fff', 0.15)
                        : alpha('#fff', 0.1),
                      backdropFilter: 'blur(20px)',
                      border: plan.popular
                        ? `2px solid ${currentTheme.accent}`
                        : `1px solid ${alpha('#fff', 0.2)}`,
                      borderRadius: 3,
                      position: 'relative',
                      transform: plan.popular ? 'scale(1.05)' : 'scale(1)',
                      '&:hover': {
                        transform: plan.popular ? 'scale(1.08)' : 'scale(1.03)',
                        transition: 'transform 0.3s ease'
                      }
                    }}
                  >
                    {plan.popular && (
                      <Chip
                        label="Most Popular"
                        sx={{
                          position: 'absolute',
                          top: -12,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          background: currentTheme.accent,
                          color: currentTheme.primary,
                          fontWeight: 'bold'
                        }}
                      />
                    )}

                    <CardContent sx={{ p: 4, textAlign: 'center' }}>
                      <Typography
                        variant="h4"
                        sx={{ color: 'white', mb: 2, fontWeight: 'bold' }}
                      >
                        {plan.name}
                      </Typography>

                      <Typography
                        variant="h3"
                        sx={{
                          color: currentTheme.accent,
                          mb: 4,
                          fontWeight: 'bold'
                        }}
                      >
                        {plan.price}
                      </Typography>

                      <Box sx={{ mb: 4 }}>
                        {plan.features.map((feature, featureIndex) => (
                          <Box
                            key={featureIndex}
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              mb: 2
                            }}
                          >
                            <CheckCircle
                              sx={{
                                color: currentTheme.accent,
                                mr: 2,
                                fontSize: 20
                              }}
                            />
                            <Typography
                              variant="body1"
                              sx={{ color: alpha('#fff', 0.9) }}
                            >
                              {feature}
                            </Typography>
                          </Box>
                        ))}
                      </Box>

                      <Button
                        variant={plan.popular ? 'contained' : 'outlined'}
                        fullWidth
                        size="large"
                        onClick={() => navigate(plan.name === 'BETA' ? '/beta-login' : '/login')}
                        sx={{
                          py: 2,
                          fontSize: '1.1rem',
                          fontWeight: 'bold',
                          ...(plan.popular ? {
                            background: currentTheme.accent,
                            color: currentTheme.primary,
                            '&:hover': {
                              background: alpha(currentTheme.accent, 0.9)
                            }
                          } : {
                            color: 'white',
                            borderColor: 'white',
                            '&:hover': {
                              borderColor: 'white',
                              background: alpha('#fff', 0.1)
                            }
                          })
                        }}
                      >
                        {plan.name === 'BETA' ? 'Start Free Trial' : 'Get Started'}
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Floating Theme Selector */}
      <Fab
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          background: currentTheme.accent,
          color: currentTheme.primary,
          '&:hover': {
            background: alpha(currentTheme.accent, 0.9)
          }
        }}
        onClick={(e) => setThemeAnchor(e.currentTarget)}
      >
        <Palette />
      </Fab>
    </Box>
  );
};

export default LandingPage;
