import React, { useEffect } from 'react';

const SimpleBetaLogin: React.FC = () => {
  useEffect(() => {
    // Auto redirect to beta dashboard after 2 seconds
    const timer = setTimeout(() => {
      window.location.href = '/beta-dashboard';
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #FF9800, #FFA726)',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        padding: '3rem',
        borderRadius: '15px',
        textAlign: 'center',
        maxWidth: '400px',
        width: '100%'
      }}>
        <h1 style={{ marginBottom: '2rem' }}>🚀 AgriIntel BETA</h1>
        
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          padding: '2rem',
          borderRadius: '10px',
          marginBottom: '2rem'
        }}>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🎉</div>
          <h2>Welcome to BETA!</h2>
          <p>Accessing limited features...</p>
        </div>

        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '0.5rem',
          marginBottom: '2rem'
        }}>
          <div style={{
            width: '10px',
            height: '10px',
            background: '#FFD700',
            borderRadius: '50%',
            animation: 'pulse 1s infinite'
          }}></div>
          <div style={{
            width: '10px',
            height: '10px',
            background: '#FFD700',
            borderRadius: '50%',
            animation: 'pulse 1s infinite 0.2s'
          }}></div>
          <div style={{
            width: '10px',
            height: '10px',
            background: '#FFD700',
            borderRadius: '50%',
            animation: 'pulse 1s infinite 0.4s'
          }}></div>
        </div>

        <a
          href="/beta-dashboard"
          style={{
            padding: '1rem 2rem',
            background: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            textDecoration: 'none',
            fontSize: '1.1rem',
            fontWeight: 'bold',
            display: 'inline-block'
          }}
        >
          Go to BETA Dashboard
        </a>

        <div style={{ marginTop: '2rem', opacity: 0.8 }}>
          <p>🎯 BETA Features Available:</p>
          <ul style={{ listStyle: 'none', padding: 0 }}>
            <li>✅ Up to 50 animals</li>
            <li>✅ Basic health monitoring</li>
            <li>✅ Simple reports</li>
          </ul>
        </div>
      </div>

      <style>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default SimpleBetaLogin;
