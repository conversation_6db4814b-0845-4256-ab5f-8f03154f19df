/**
 * Advanced Analytics Service
 * 
 * Service for consuming advanced analytics endpoints powered by MongoDB Atlas SQL
 */

import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

export interface LivestockPerformanceData {
  animalId: string;
  tagNumber: string;
  breed: string;
  age: number;
  weight: number;
  healthScore: number;
  breedingSuccess: number;
  breedingSuccessRate: number;
}

export interface FinancialAnalyticsData {
  _id: {
    year: number;
    month: number;
  };
  categories: Array<{
    category: string;
    amount: number;
    count: number;
  }>;
  totalMonthly: number;
}

export interface HealthTrendsData {
  _id: {
    year: number;
    month: number;
  };
  conditions: Array<{
    condition: string;
    cases: number;
    avgSeverity: number;
  }>;
  totalCases: number;
}

export interface BreedingEfficiencyData {
  _id: {
    breed: string;
    year: number;
  };
  totalBreedings: number;
  successfulBreedings: number;
  avgGestationPeriod: number;
  successRate: number;
}

export interface FeedEfficiencyData {
  _id: {
    animalId: string;
    feedType: string;
    month: number;
    year: number;
  };
  totalFeedConsumed: number;
  feedingDays: number;
  avgWeightGain: number;
  feedEfficiency: number;
}

export interface DashboardSummary {
  totalAnimals: number;
  avgHealthScore: number;
  avgBreedingSuccessRate: number;
  topPerformingBreeds: BreedingEfficiencyData[];
  recentHealthTrends: HealthTrendsData[];
  feedEfficiencyLeaders: FeedEfficiencyData[];
}

export interface AnalyticsResponse<T> {
  success: boolean;
  data: T;
  message: string;
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
}

class AnalyticsService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Get livestock performance analytics
   */
  async getLivestockPerformance(): Promise<LivestockPerformanceData[]> {
    try {
      const response = await axios.get<AnalyticsResponse<LivestockPerformanceData[]>>(
        `${API_URL}/analytics/livestock-performance`,
        { headers: this.getAuthHeaders() }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching livestock performance analytics:', error);
      throw new Error('Failed to fetch livestock performance analytics');
    }
  }

  /**
   * Get financial analytics for a date range
   */
  async getFinancialAnalytics(startDate?: Date, endDate?: Date): Promise<FinancialAnalyticsData[]> {
    try {
      const params: any = {};
      if (startDate) params.startDate = startDate.toISOString();
      if (endDate) params.endDate = endDate.toISOString();

      const response = await axios.get<AnalyticsResponse<FinancialAnalyticsData[]>>(
        `${API_URL}/analytics/financial`,
        { 
          headers: this.getAuthHeaders(),
          params
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching financial analytics:', error);
      throw new Error('Failed to fetch financial analytics');
    }
  }

  /**
   * Get health trends analytics
   */
  async getHealthTrends(): Promise<HealthTrendsData[]> {
    try {
      const response = await axios.get<AnalyticsResponse<HealthTrendsData[]>>(
        `${API_URL}/analytics/health-trends`,
        { headers: this.getAuthHeaders() }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching health trends analytics:', error);
      throw new Error('Failed to fetch health trends analytics');
    }
  }

  /**
   * Get breeding efficiency analytics
   */
  async getBreedingEfficiency(): Promise<BreedingEfficiencyData[]> {
    try {
      const response = await axios.get<AnalyticsResponse<BreedingEfficiencyData[]>>(
        `${API_URL}/analytics/breeding-efficiency`,
        { headers: this.getAuthHeaders() }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching breeding efficiency analytics:', error);
      throw new Error('Failed to fetch breeding efficiency analytics');
    }
  }

  /**
   * Get feed efficiency analytics
   */
  async getFeedEfficiency(): Promise<FeedEfficiencyData[]> {
    try {
      const response = await axios.get<AnalyticsResponse<FeedEfficiencyData[]>>(
        `${API_URL}/analytics/feed-efficiency`,
        { headers: this.getAuthHeaders() }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching feed efficiency analytics:', error);
      throw new Error('Failed to fetch feed efficiency analytics');
    }
  }

  /**
   * Get comprehensive dashboard analytics summary
   */
  async getDashboardSummary(): Promise<{
    summary: DashboardSummary;
    details: {
      livestockPerformance: LivestockPerformanceData[];
      healthTrends: HealthTrendsData[];
      breedingEfficiency: BreedingEfficiencyData[];
      feedEfficiency: FeedEfficiencyData[];
    };
  }> {
    try {
      const response = await axios.get<AnalyticsResponse<{
        summary: DashboardSummary;
        details: {
          livestockPerformance: LivestockPerformanceData[];
          healthTrends: HealthTrendsData[];
          breedingEfficiency: BreedingEfficiencyData[];
          feedEfficiency: FeedEfficiencyData[];
        };
      }>>(
        `${API_URL}/analytics/dashboard-summary`,
        { headers: this.getAuthHeaders() }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching dashboard analytics summary:', error);
      throw new Error('Failed to fetch dashboard analytics summary');
    }
  }

  /**
   * Execute custom analytics query (Admin only)
   */
  async executeCustomQuery(collection: string, pipeline: any[]): Promise<any[]> {
    try {
      const response = await axios.post<AnalyticsResponse<any[]>>(
        `${API_URL}/analytics/custom-query`,
        { collection, pipeline },
        { headers: this.getAuthHeaders() }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error executing custom analytics query:', error);
      throw new Error('Failed to execute custom analytics query');
    }
  }

  /**
   * Get chart-ready data for livestock performance
   */
  async getLivestockPerformanceChartData() {
    const data = await this.getLivestockPerformance();
    
    return {
      labels: data.map(animal => animal.tagNumber),
      datasets: [
        {
          label: 'Health Score',
          data: data.map(animal => animal.healthScore || 0),
          backgroundColor: 'rgba(34, 197, 94, 0.8)',
          borderColor: 'rgba(34, 197, 94, 1)',
        },
        {
          label: 'Breeding Success Rate',
          data: data.map(animal => (animal.breedingSuccessRate || 0) * 100),
          backgroundColor: 'rgba(59, 130, 246, 0.8)',
          borderColor: 'rgba(59, 130, 246, 1)',
        }
      ]
    };
  }

  /**
   * Get chart-ready data for financial analytics
   */
  async getFinancialChartData(startDate?: Date, endDate?: Date) {
    const data = await this.getFinancialAnalytics(startDate, endDate);
    
    const labels = data.map(item => `${item._id.year}-${String(item._id.month).padStart(2, '0')}`);
    const totalAmounts = data.map(item => item.totalMonthly);
    
    return {
      labels,
      datasets: [
        {
          label: 'Monthly Total',
          data: totalAmounts,
          backgroundColor: 'rgba(168, 85, 247, 0.8)',
          borderColor: 'rgba(168, 85, 247, 1)',
        }
      ]
    };
  }

  /**
   * Get chart-ready data for health trends
   */
  async getHealthTrendsChartData() {
    const data = await this.getHealthTrends();
    
    const labels = data.map(item => `${item._id.year}-${String(item._id.month).padStart(2, '0')}`);
    const totalCases = data.map(item => item.totalCases);
    
    return {
      labels,
      datasets: [
        {
          label: 'Total Health Cases',
          data: totalCases,
          backgroundColor: 'rgba(239, 68, 68, 0.8)',
          borderColor: 'rgba(239, 68, 68, 1)',
        }
      ]
    };
  }
}

export const analyticsService = new AnalyticsService();
