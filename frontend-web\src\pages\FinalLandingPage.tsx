import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const FinalLandingPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('features');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Professional livestock images for background showcase
  const livestockImages = [
    'https://wallpaperaccess.com/full/7985150.jpg', // Beautiful cattle in field
    'https://wallpaperaccess.com/full/2973889.jpg', // Professional cow portrait
    'https://wallpaperaccess.com/full/7985153.jpg', // Cattle herd landscape
    'https://wallpaperaccess.com/full/6946250.jpg', // Premium cattle wallpaper
    'https://wallpaperaccess.com/full/7985155.jpg', // Cattle in natural setting
    'https://wallpaperaccess.com/full/1137850.jpg', // Cute cow close-up
    'https://wallpaperaccess.com/full/7985186.jpg', // Cattle ranch scene
    'https://wallpaperaccess.com/full/7985190.jpg', // Cows in Alps mountains
    'https://wallpaperaccess.com/full/3174996.jpg', // Longhorn cattle HD
    'https://wallpaperaccess.com/full/2390117.jpg', // Baby farm animals
    'https://wallpaperaccess.com/full/4913590.jpg', // Professional animal wallpaper
    'https://wallpaperaccess.com/full/7985232.jpg'  // Beautiful livestock scene
  ];

  // Auto-rotate images
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % livestockImages.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [livestockImages.length]);

  const features = [
    { icon: '🐄', title: 'Smart Livestock Tracking', description: 'Advanced RFID and IoT sensors for real-time monitoring of cattle, sheep, and goats across your South African farm operations.' },
    { icon: '🩺', title: 'AI Health Management', description: 'AI-powered health diagnostics and predictive analytics to prevent diseases and reduce veterinary costs by up to 40%.' },
    { icon: '📊', title: 'Advanced Analytics', description: 'Comprehensive insights and reporting tools specifically designed for South African livestock operations.' },
    { icon: '💰', title: 'Financial Optimization', description: 'Smart financial planning and cost optimization tools tailored for South African farming with ZAR currency support.' },
    { icon: '🔒', title: 'Enterprise Security', description: 'Bank-grade security with local South African data centers, cloud backup, and 99.9% uptime guarantee.' },
    { icon: '🌱', title: 'Sustainable Farming', description: 'Environmental monitoring and sustainable farming practices designed for South African climate conditions.' }
  ];

  const stats = [
    { number: '15K+', label: 'South African Farmers', icon: '👨‍🌾' },
    { number: '750K+', label: 'Livestock Tracked', icon: '🐄' },
    { number: '99.9%', label: 'System Uptime', icon: '⚡' },
    { number: '40%', label: 'Cost Reduction', icon: '💰' }
  ];

  const subscriptionTiers = [
    {
      name: 'Beta Access',
      price: 'Free',
      duration: '30 days trial',
      description: 'Perfect for small-scale South African farmers',
      features: [
        '🐄 Up to 50 animals',
        '📱 Mobile app access',
        '📊 Basic health monitoring',
        '📈 Simple reports',
        '📧 Email support',
        '🇿🇦 South African language support'
      ],
      buttonText: 'START BETA',
      buttonAction: () => navigate('/beta-login'),
      popular: false
    },
    {
      name: 'Professional',
      price: 'R299',
      duration: 'per month',
      description: 'For growing commercial farms across SA',
      features: [
        '🐄 Up to 500 animals',
        '🤖 AI health analytics',
        '💰 Financial management (ZAR)',
        '🧬 Breeding optimization',
        '📞 Priority support',
        '📊 Custom reports',
        '🔗 API access',
        '🌍 Multi-location support'
      ],
      buttonText: 'GO LIVE',
      buttonAction: () => navigate('/login'),
      popular: true
    },
    {
      name: 'Enterprise',
      price: 'R599',
      duration: 'per month',
      description: 'For large commercial operations & cooperatives',
      features: [
        '🐄 Unlimited animals',
        '🤖 AI-powered insights',
        '🏢 Multi-farm management',
        '📊 Advanced analytics',
        '👨‍💼 Dedicated account manager',
        '🔧 Custom integrations',
        '🏷️ White-label options',
        '🎓 Training & onboarding'
      ],
      buttonText: 'GO LIVE',
      buttonAction: () => navigate('/login'),
      popular: false
    }
  ];

  const containerStyle: React.CSSProperties = {
    minHeight: '100vh',
    background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 25%, #4CAF50 50%, #66BB6A 75%, #81C784 100%)',
    color: 'white',
    fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
    lineHeight: 1.6
  };

  const navStyle: React.CSSProperties = {
    padding: '1rem 2rem',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    background: 'rgba(0,0,0,0.2)',
    backdropFilter: 'blur(10px)',
    borderBottom: '1px solid rgba(255,255,255,0.1)'
  };

  const heroStyle: React.CSSProperties = {
    padding: '6rem 2rem 4rem',
    textAlign: 'center',
    maxWidth: '1400px',
    margin: '0 auto'
  };

  const buttonBaseStyle: React.CSSProperties = {
    padding: '1rem 2rem',
    margin: '0.5rem',
    border: 'none',
    borderRadius: '12px',
    cursor: 'pointer',
    fontWeight: 'bold',
    fontSize: '1.1rem',
    transition: 'all 0.3s ease',
    textTransform: 'uppercase',
    letterSpacing: '1px'
  };

  const primaryButtonStyle: React.CSSProperties = {
    ...buttonBaseStyle,
    background: 'linear-gradient(45deg, #FF6B35, #FF8E53)',
    color: 'white',
    boxShadow: '0 4px 15px rgba(255, 107, 53, 0.4)'
  };

  const secondaryButtonStyle: React.CSSProperties = {
    ...buttonBaseStyle,
    background: 'transparent',
    color: 'white',
    border: '2px solid white',
    boxShadow: '0 4px 15px rgba(255, 255, 255, 0.2)'
  };

  const cardStyle: React.CSSProperties = {
    background: 'rgba(255,255,255,0.1)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255,255,255,0.2)',
    borderRadius: '16px',
    padding: '2rem',
    textAlign: 'center',
    transition: 'all 0.3s ease'
  };

  return (
    <div style={containerStyle}>
      {/* Navigation */}
      <nav style={navStyle}>
        <div style={{ 
          fontSize: '2.5rem', 
          fontWeight: 'bold',
          background: 'linear-gradient(45deg, #FFD700, #FFA500)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
        }}>
          🌾 AgriIntel
        </div>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <button 
            style={primaryButtonStyle}
            onClick={() => navigate('/beta-login')}
            onMouseOver={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
            onMouseOut={(e) => e.currentTarget.style.transform = 'translateY(0)'}
          >
            🚀 BETA
          </button>
          <button 
            style={secondaryButtonStyle}
            onClick={() => navigate('/login')}
            onMouseOver={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
            onMouseOut={(e) => e.currentTarget.style.transform = 'translateY(0)'}
          >
            ⚡ LIVE
          </button>
        </div>
      </nav>

      {/* Hero Section */}
      <section style={heroStyle}>
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          padding: '1rem 2rem',
          borderRadius: '50px',
          display: 'inline-block',
          marginBottom: '2rem',
          border: '1px solid rgba(255,255,255,0.2)'
        }}>
          🏆 SOUTH AFRICA'S #1 LIVESTOCK MANAGEMENT PLATFORM
        </div>

        <h1 style={{ 
          fontSize: 'clamp(2.5rem, 5vw, 4rem)', 
          marginBottom: '1.5rem',
          fontWeight: 'bold',
          textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
          lineHeight: 1.2
        }}>
          🌾 REVOLUTIONIZE YOUR<br />
          <span style={{
            background: 'linear-gradient(45deg, #FFD700, #FFA500)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            LIVESTOCK FARM
          </span><br />
          WITH AI INTELLIGENCE
        </h1>
        
        <p style={{ 
          fontSize: 'clamp(1.2rem, 2.5vw, 1.8rem)', 
          marginBottom: '3rem', 
          opacity: 0.95,
          maxWidth: '800px',
          margin: '0 auto 3rem'
        }}>
          Join 15,000+ South African farmers who've transformed their operations.<br />
          Monitor 750,000+ animals, reduce costs by 40%, and maximize productivity.
        </p>

        <div style={{ marginBottom: '4rem' }}>
          <button
            style={{
              ...primaryButtonStyle,
              fontSize: '1.3rem',
              padding: '1.5rem 3rem',
              marginRight: '1rem'
            }}
            onClick={() => navigate('/beta-login')}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-3px)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 107, 53, 0.6)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 15px rgba(255, 107, 53, 0.4)';
            }}
          >
            🚀 START FREE BETA
          </button>
          <button
            style={{
              ...secondaryButtonStyle,
              fontSize: '1.3rem',
              padding: '1.5rem 3rem'
            }}
            onClick={() => navigate('/login')}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-3px)';
              e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.background = 'transparent';
            }}
          >
            ⚡ GO LIVE NOW
          </button>
        </div>

        {/* Stats Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '2rem',
          marginTop: '4rem'
        }}>
          {stats.map((stat, index) => (
            <div
              key={index}
              style={{
                ...cardStyle,
                cursor: 'pointer'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.background = 'rgba(255,255,255,0.15)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
              }}
            >
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>{stat.icon}</div>
              <div style={{ 
                fontSize: '2.5rem', 
                fontWeight: 'bold', 
                marginBottom: '0.5rem',
                background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                {stat.number}
              </div>
              <div style={{ opacity: 0.9, fontSize: '1.1rem' }}>{stat.label}</div>
            </div>
          ))}
        </div>
      </section>

      {/* Livestock Showcase Section */}
      <section style={{
        background: 'rgba(0,0,0,0.3)',
        padding: '4rem 2rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Background Image */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${livestockImages[currentImageIndex]})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          opacity: 0.3,
          transition: 'all 1s ease-in-out',
          zIndex: 0
        }} />

        {/* Overlay */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(135deg, rgba(27, 94, 32, 0.8) 0%, rgba(46, 125, 50, 0.6) 100%)',
          zIndex: 1
        }} />

        <div style={{
          maxWidth: '1400px',
          margin: '0 auto',
          position: 'relative',
          zIndex: 2,
          textAlign: 'center'
        }}>
          <h2 style={{
            fontSize: '3rem',
            marginBottom: '2rem',
            background: 'linear-gradient(45deg, #FFD700, #FFA500)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
          }}>
            🐄 REAL SOUTH AFRICAN LIVESTOCK SUCCESS
          </h2>

          <p style={{
            fontSize: '1.5rem',
            marginBottom: '3rem',
            opacity: 0.95,
            maxWidth: '800px',
            margin: '0 auto 3rem',
            textShadow: '1px 1px 2px rgba(0,0,0,0.7)'
          }}>
            See how AgriIntel transforms real farms across South Africa with cutting-edge technology
          </p>

          {/* Image Gallery Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '2rem',
            marginBottom: '3rem'
          }}>
            {livestockImages.slice(0, 6).map((image, index) => (
              <div
                key={index}
                style={{
                  position: 'relative',
                  height: '250px',
                  borderRadius: '16px',
                  overflow: 'hidden',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  border: currentImageIndex === index ? '3px solid #FFD700' : '1px solid rgba(255,255,255,0.2)',
                  boxShadow: currentImageIndex === index
                    ? '0 8px 32px rgba(255, 215, 0, 0.4)'
                    : '0 4px 16px rgba(0,0,0,0.3)'
                }}
                onClick={() => setCurrentImageIndex(index)}
                onMouseOver={(e) => {
                  e.currentTarget.style.transform = 'scale(1.05)';
                  e.currentTarget.style.boxShadow = '0 12px 40px rgba(255, 215, 0, 0.3)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.transform = 'scale(1)';
                  e.currentTarget.style.boxShadow = currentImageIndex === index
                    ? '0 8px 32px rgba(255, 215, 0, 0.4)'
                    : '0 4px 16px rgba(0,0,0,0.3)';
                }}
              >
                <img
                  src={image}
                  alt={`Livestock ${index + 1}`}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
                <div style={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
                  color: 'white',
                  padding: '1rem',
                  textAlign: 'center'
                }}>
                  <div style={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                    {['Premium Cattle', 'Dairy Excellence', 'Ranch Operations', 'Livestock Health', 'Farm Management', 'Smart Monitoring'][index]}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '1rem',
            marginBottom: '2rem'
          }}>
            <button
              style={{
                ...primaryButtonStyle,
                fontSize: '1.2rem',
                padding: '1rem 2rem'
              }}
              onClick={() => navigate('/beta-login')}
            >
              🚀 START YOUR SUCCESS STORY
            </button>
            <button
              style={{
                ...secondaryButtonStyle,
                fontSize: '1.2rem',
                padding: '1rem 2rem'
              }}
              onClick={() => navigate('/login')}
            >
              📊 VIEW LIVE DEMO
            </button>
          </div>
        </div>
      </section>

      {/* Tabbed Content Section */}
      <section style={{
        background: 'rgba(0,0,0,0.2)',
        padding: '4rem 2rem',
        margin: '3rem 0'
      }}>
        <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
          {/* Tab Navigation */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '1rem',
            marginBottom: '3rem',
            flexWrap: 'wrap'
          }}>
            {['features', 'pricing', 'about', 'contact'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                style={{
                  padding: '1rem 2rem',
                  background: activeTab === tab 
                    ? 'linear-gradient(45deg, #FF6B35, #FF8E53)' 
                    : 'transparent',
                  color: 'white',
                  border: '2px solid #FF6B35',
                  borderRadius: '30px',
                  cursor: 'pointer',
                  fontWeight: 'bold',
                  textTransform: 'capitalize',
                  fontSize: '1.1rem',
                  transition: 'all 0.3s ease'
                }}
                onMouseOver={(e) => {
                  if (activeTab !== tab) {
                    e.currentTarget.style.background = 'rgba(255, 107, 53, 0.2)';
                  }
                }}
                onMouseOut={(e) => {
                  if (activeTab !== tab) {
                    e.currentTarget.style.background = 'transparent';
                  }
                }}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ textAlign: 'center' }}>
            {activeTab === 'features' && (
              <div>
                <h2 style={{ 
                  fontSize: '2.5rem', 
                  marginBottom: '3rem',
                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  Powerful Features for Modern Farmers
                </h2>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
                  gap: '2rem'
                }}>
                  {features.map((feature, index) => (
                    <div 
                      key={index} 
                      style={{
                        ...cardStyle,
                        cursor: 'pointer'
                      }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.transform = 'translateY(-8px)';
                        e.currentTarget.style.background = 'rgba(255,255,255,0.15)';
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
                      }}
                    >
                      <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>{feature.icon}</div>
                      <h3 style={{ 
                        marginBottom: '1rem', 
                        fontSize: '1.3rem',
                        color: '#FFD700'
                      }}>
                        {feature.title}
                      </h3>
                      <p style={{ opacity: 0.9, lineHeight: 1.6 }}>{feature.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'pricing' && (
              <div>
                <h2 style={{ 
                  fontSize: '2.5rem', 
                  marginBottom: '3rem',
                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  Choose Your Perfect Plan
                </h2>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
                  gap: '2rem',
                  maxWidth: '1200px',
                  margin: '0 auto'
                }}>
                  {subscriptionTiers.map((tier, index) => (
                    <div 
                      key={index} 
                      style={{
                        ...cardStyle,
                        border: tier.popular ? '3px solid #FFD700' : '1px solid rgba(255,255,255,0.2)',
                        position: 'relative',
                        cursor: 'pointer',
                        transform: tier.popular ? 'scale(1.05)' : 'scale(1)'
                      }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.transform = tier.popular ? 'scale(1.08)' : 'scale(1.03)';
                        e.currentTarget.style.background = 'rgba(255,255,255,0.15)';
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.transform = tier.popular ? 'scale(1.05)' : 'scale(1)';
                        e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
                      }}
                    >
                      {tier.popular && (
                        <div style={{
                          position: 'absolute',
                          top: '-15px',
                          left: '50%',
                          transform: 'translateX(-50%)',
                          background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                          color: '#1B5E20',
                          padding: '0.5rem 1.5rem',
                          borderRadius: '20px',
                          fontWeight: 'bold',
                          fontSize: '0.9rem'
                        }}>
                          ⭐ MOST POPULAR
                        </div>
                      )}
                      <h3 style={{ 
                        marginBottom: '1rem', 
                        fontSize: '1.5rem',
                        color: '#FFD700'
                      }}>
                        {tier.name}
                      </h3>
                      <div style={{ 
                        fontSize: '2.5rem', 
                        fontWeight: 'bold', 
                        color: '#FF6B35', 
                        marginBottom: '0.5rem' 
                      }}>
                        {tier.price}
                      </div>
                      <div style={{ 
                        opacity: 0.8, 
                        marginBottom: '1.5rem',
                        fontSize: '0.9rem'
                      }}>
                        {tier.duration}
                      </div>
                      <p style={{ marginBottom: '2rem', opacity: 0.9 }}>{tier.description}</p>
                      <ul style={{ 
                        listStyle: 'none', 
                        padding: 0, 
                        marginBottom: '2rem',
                        textAlign: 'left'
                      }}>
                        {tier.features.map((feature, i) => (
                          <li key={i} style={{ 
                            padding: '0.5rem 0',
                            borderBottom: '1px solid rgba(255,255,255,0.1)'
                          }}>
                            ✅ {feature}
                          </li>
                        ))}
                      </ul>
                      <button
                        onClick={tier.buttonAction}
                        style={{
                          width: '100%',
                          padding: '1rem',
                          background: tier.popular 
                            ? 'linear-gradient(45deg, #FFD700, #FFA500)'
                            : 'linear-gradient(45deg, #FF6B35, #FF8E53)',
                          color: tier.popular ? '#1B5E20' : 'white',
                          border: 'none',
                          borderRadius: '12px',
                          cursor: 'pointer',
                          fontWeight: 'bold',
                          fontSize: '1.1rem',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseOver={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 107, 53, 0.4)';
                        }}
                        onMouseOut={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      >
                        {tier.buttonText}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'about' && (
              <div>
                <h2 style={{ 
                  fontSize: '2.5rem', 
                  marginBottom: '2rem',
                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  About AgriIntel
                </h2>
                <div style={{
                  maxWidth: '800px',
                  margin: '0 auto',
                  ...cardStyle
                }}>
                  <p style={{ 
                    fontSize: '1.3rem', 
                    marginBottom: '2rem', 
                    opacity: 0.95,
                    lineHeight: 1.8
                  }}>
                    South Africa's leading livestock management platform, empowering farmers 
                    with AI-driven insights and comprehensive farm management solutions. 
                    Built specifically for South African conditions, supporting local currencies, 
                    languages, and agricultural practices.
                  </p>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                    gap: '2rem',
                    marginTop: '3rem'
                  }}>
                    {stats.map((stat, index) => (
                      <div key={index} style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>{stat.icon}</div>
                        <div style={{ 
                          fontSize: '1.8rem', 
                          fontWeight: 'bold', 
                          color: '#FFD700',
                          marginBottom: '0.5rem'
                        }}>
                          {stat.number}
                        </div>
                        <div style={{ opacity: 0.8 }}>{stat.label}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'contact' && (
              <div>
                <h2 style={{ 
                  fontSize: '2.5rem', 
                  marginBottom: '3rem',
                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  Get in Touch
                </h2>
                <div style={{
                  maxWidth: '600px',
                  margin: '0 auto',
                  ...cardStyle
                }}>
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '2rem'
                  }}>
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: '1rem',
                      padding: '1rem',
                      background: 'rgba(255,255,255,0.1)',
                      borderRadius: '12px'
                    }}>
                      <span style={{ fontSize: '2rem' }}>📞</span>
                      <div>
                        <div style={{ fontWeight: 'bold', color: '#FFD700' }}>Phone</div>
                        <div>+27 11 123 4567</div>
                      </div>
                    </div>
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: '1rem',
                      padding: '1rem',
                      background: 'rgba(255,255,255,0.1)',
                      borderRadius: '12px'
                    }}>
                      <span style={{ fontSize: '2rem' }}>📧</span>
                      <div>
                        <div style={{ fontWeight: 'bold', color: '#FFD700' }}>Email</div>
                        <div><EMAIL></div>
                      </div>
                    </div>
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: '1rem',
                      padding: '1rem',
                      background: 'rgba(255,255,255,0.1)',
                      borderRadius: '12px'
                    }}>
                      <span style={{ fontSize: '2rem' }}>📍</span>
                      <div>
                        <div style={{ fontWeight: 'bold', color: '#FFD700' }}>Location</div>
                        <div>Cape Town, South Africa</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer style={{
        background: 'rgba(0,0,0,0.3)',
        padding: '3rem 2rem 2rem',
        textAlign: 'center',
        borderTop: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
          <div style={{ marginBottom: '2rem' }}>
            <div style={{ 
              fontSize: '2rem', 
              fontWeight: 'bold', 
              marginBottom: '1rem',
              background: 'linear-gradient(45deg, #FFD700, #FFA500)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              🌾 AgriIntel
            </div>
            <p style={{ 
              opacity: 0.9, 
              fontSize: '1.1rem',
              maxWidth: '600px',
              margin: '0 auto'
            }}>
              Smart Livestock Management for South African Farmers.<br />
              Empowering agriculture with AI technology and data-driven insights.
            </p>
          </div>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '2rem',
            marginBottom: '2rem'
          }}>
            <div>
              <h4 style={{ color: '#FFD700', marginBottom: '1rem' }}>Platform</h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <button 
                  style={{ 
                    background: 'none', 
                    border: 'none', 
                    color: 'white', 
                    cursor: 'pointer',
                    opacity: 0.8,
                    fontSize: '1rem'
                  }}
                  onClick={() => navigate('/beta-login')}
                >
                  Beta Access
                </button>
                <button 
                  style={{ 
                    background: 'none', 
                    border: 'none', 
                    color: 'white', 
                    cursor: 'pointer',
                    opacity: 0.8,
                    fontSize: '1rem'
                  }}
                  onClick={() => navigate('/login')}
                >
                  Live Access
                </button>
              </div>
            </div>
            <div>
              <h4 style={{ color: '#FFD700', marginBottom: '1rem' }}>Support</h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <span style={{ opacity: 0.8 }}>Documentation</span>
                <span style={{ opacity: 0.8 }}>Help Center</span>
                <span style={{ opacity: 0.8 }}>Training</span>
              </div>
            </div>
            <div>
              <h4 style={{ color: '#FFD700', marginBottom: '1rem' }}>Company</h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <span style={{ opacity: 0.8 }}>About Us</span>
                <span style={{ opacity: 0.8 }}>Careers</span>
                <span style={{ opacity: 0.8 }}>Partners</span>
              </div>
            </div>
          </div>
          
          <div style={{ 
            borderTop: '1px solid rgba(255,255,255,0.1)',
            paddingTop: '2rem',
            opacity: 0.7
          }}>
            © 2024 AgriIntel. All rights reserved. Made with ❤️ for South African farmers.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default FinalLandingPage;
