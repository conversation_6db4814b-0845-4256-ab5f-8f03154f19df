import React from 'react';
import { useNavigate } from 'react-router-dom';

const PremiumLandingPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div>
      <h1>🌾 AgriIntel - Smart Livestock Management</h1>
      <p>Welcome to South Africa's leading livestock management platform!</p>

      <div>
        <button onClick={() => navigate('/beta-login')}>
          BETA ACCESS
        </button>
        <button onClick={() => navigate('/login')}>
          LIVE ACCESS
        </button>
      </div>

      <div>
        <h2>Features</h2>
        <ul>
          <li>🐄 Smart Livestock Tracking</li>
          <li>🩺 AI Health Management</li>
          <li>📊 Advanced Analytics</li>
          <li>💰 Financial Optimization</li>
        </ul>
      </div>

      <div>
        <h2>Pricing</h2>
        <div>
          <h3>BETA - Free</h3>
          <p>Perfect for small-scale farmers</p>
          <button onClick={() => navigate('/beta-login')}>Get Started</button>
        </div>
        <div>
          <h3>Professional - R299/month</h3>
          <p>For growing commercial farms</p>
          <button onClick={() => navigate('/login')}>Get Started</button>
        </div>
      </div>
    </div>
  );

export default PremiumLandingPage;

export default PremiumLandingPage;
