import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  Agriculture,
  TrendingUp,
  Security,
  Analytics,
  Pets,
  LocalHospital,
  PlayArrow,
  ArrowForward,
  CheckCircle,
  Star,
  Phone,
  Email,
  LocationOn,
  Facebook,
  Twitter,
  LinkedIn,
  Instagram
} from '@mui/icons-material';
import '../styles/stable-landing.css';

const PremiumLandingPage: React.FC = () => {
  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeTab, setActiveTab] = useState('features');

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const features = [
    {
      icon: <Pets />,
      title: '🐄 Smart Livestock Tracking',
      description: 'Advanced RFID and IoT sensors for real-time monitoring of cattle, sheep, and goats. Track health, location, and behavior patterns across your South African farm operations.'
    },
    {
      icon: <LocalHospital />,
      title: '🩺 AI Health Management',
      description: 'AI-powered health diagnostics and predictive analytics to prevent diseases, optimize animal welfare, and reduce veterinary costs by up to 40%.'
    },
    {
      icon: <Analytics />,
      title: '📊 Advanced Analytics',
      description: 'Comprehensive insights and reporting tools specifically designed for South African livestock operations. Maximize productivity and profitability with data-driven decisions.'
    },
    {
      icon: <TrendingUp />,
      title: '💰 Financial Optimization',
      description: 'Smart financial planning and cost optimization tools tailored for South African farming. Track expenses, revenue, and ROI with currency-specific features.'
    },
    {
      icon: <Security />,
      title: '🔒 Enterprise Security',
      description: 'Bank-grade security with local South African data centers, cloud backup, and 99.9% uptime guarantee for your critical farm data.'
    },
    {
      icon: <Agriculture />,
      title: '🌱 Sustainable Farming',
      description: 'Environmental monitoring and sustainable farming practices designed for South African climate conditions. Reduce carbon footprint and improve efficiency.'
    }
  ];

  const stats = [
    { number: '15K+', label: 'South African Farmers', icon: '👨‍🌾' },
    { number: '750K+', label: 'Livestock Tracked', icon: '🐄' },
    { number: '99.9%', label: 'System Uptime', icon: '⚡' },
    { number: '40%', label: 'Cost Reduction', icon: '💰' }
  ];

  const testimonials = [
    {
      name: 'Johan van der Merwe',
      role: 'Commercial Farmer, Western Cape',
      image: '/images/testimonials/farmer1.jpg',
      quote: 'AgriIntel transformed our 500-head cattle operation. We reduced veterinary costs by 35% and increased productivity by 28%. The AI health monitoring is incredible!',
      rating: 5
    },
    {
      name: 'Nomsa Mthembu',
      role: 'Cooperative Manager, KwaZulu-Natal',
      image: '/images/testimonials/farmer2.jpg',
      quote: 'Managing 12 small farms was a nightmare before AgriIntel. Now we track 2000+ animals seamlessly. The mobile app works perfectly even in remote areas.',
      rating: 5
    },
    {
      name: 'Pieter Botha',
      role: 'Dairy Farmer, Free State',
      image: '/images/testimonials/farmer3.jpg',
      quote: 'The breeding optimization feature helped us improve our calving rate by 22%. ROI was achieved within 6 months. Best investment we ever made!',
      rating: 5
    }
  ];

  const galleryImages = [
    { src: '/images/gallery/cattle-farm-1.jpg', alt: 'Modern cattle farm with AgriIntel monitoring', category: 'farms' },
    { src: '/images/gallery/health-monitoring.jpg', alt: 'Veterinarian using AgriIntel health system', category: 'health' },
    { src: '/images/gallery/mobile-app.jpg', alt: 'Farmer using AgriIntel mobile app', category: 'technology' },
    { src: '/images/gallery/dairy-operation.jpg', alt: 'Automated dairy milking with AgriIntel', category: 'dairy' },
    { src: '/images/gallery/breeding-management.jpg', alt: 'Breeding records on AgriIntel dashboard', category: 'breeding' },
    { src: '/images/gallery/financial-dashboard.jpg', alt: 'Financial analytics and reporting', category: 'analytics' }
  ];

  const subscriptionTiers = [
    {
      name: 'Beta Access',
      price: 'Free',
      duration: '30 days trial',
      description: 'Perfect for small-scale South African farmers',
      features: [
        '🐄 Up to 50 animals',
        '📱 Mobile app access',
        '📊 Basic health monitoring',
        '📈 Simple reports',
        '📧 Email support',
        '🇿🇦 South African language support'
      ],
      buttonText: 'BETA ACCESS',
      buttonAction: () => navigate('/beta-login'),
      popular: false,
      color: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)'
    },
    {
      name: 'Professional',
      price: 'R299',
      duration: 'per month',
      description: 'For growing commercial farms across SA',
      features: [
        '🐄 Up to 500 animals',
        '🤖 AI health analytics',
        '💰 Financial management (ZAR)',
        '🧬 Breeding optimization',
        '📞 Priority support',
        '📊 Custom reports',
        '🔗 API access',
        '🌍 Multi-location support'
      ],
      buttonText: 'LIVE ACCESS',
      buttonAction: () => navigate('/login'),
      popular: true,
      color: 'linear-gradient(135deg, #2196F3 0%, #1565C0 100%)'
    },
    {
      name: 'Enterprise',
      price: 'R599',
      duration: 'per month',
      description: 'For large commercial operations & cooperatives',
      features: [
        '🐄 Unlimited animals',
        '🤖 AI-powered insights',
        '🏢 Multi-farm management',
        '📊 Advanced analytics',
        '👨‍💼 Dedicated account manager',
        '🔧 Custom integrations',
        '🏷️ White-label options',
        '🎓 Training & onboarding',
        '🏛️ Government compliance tools'
      ],
      buttonText: 'LIVE ACCESS',
      buttonAction: () => navigate('/login'),
      popular: false,
      color: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)'
    }
  ];

  return (
    <div className="premium-landing">
      {/* Premium Navigation */}
      <nav className={`premium-nav ${isScrolled ? 'scrolled' : ''}`}>
        <div className="premium-nav-content">
          <div className="premium-logo">
            AgriIntel
          </div>

          <ul className="premium-nav-links">
            <li><a href="#features" className="premium-nav-link">Features</a></li>
            <li><a href="#pricing" className="premium-nav-link">Pricing</a></li>
            <li><a href="#about" className="premium-nav-link">About</a></li>
            <li><a href="#contact" className="premium-nav-link">Contact</a></li>
          </ul>

          <div className="premium-nav-actions">
            <button
              type="button"
              className="premium-btn premium-btn-secondary"
              onClick={() => navigate('/beta-login')}
            >
              BETA
            </button>
            <button
              type="button"
              className="premium-btn premium-btn-primary"
              onClick={() => navigate('/login')}
            >
              LIVE
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="asymmetric-hero">
        <div className="hero-bg-wrapper">
          <img
            src="https://images.unsplash.com/photo-1500595046743-cd271d694d30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80"
            alt="Modern Cattle Farm"
            className="hero-bg-image"
            onError={(e) => {
              (e.target as HTMLImageElement).src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><rect fill="%234CAF50" width="1920" height="1080"/><text x="960" y="540" text-anchor="middle" fill="white" font-size="48">Modern Farm Technology</text></svg>';
            }}
          />
          <div className="hero-overlay"></div>
        </div>

        <div className="hero-layout">
          {/* Left Content Area */}
          <div className="hero-content-left">
            <div className="hero-badge">
              🌾 SOUTH AFRICA'S #1 LIVESTOCK PLATFORM
            </div>

            <h1 className="hero-title">
              REVOLUTIONIZE YOUR
              <span className="title-highlight"> LIVESTOCK FARM</span>
              WITH AI INTELLIGENCE
            </h1>

            <p className="hero-subtitle">
              Join 15,000+ South African farmers who've transformed their operations.
              Monitor 750,000+ animals, reduce costs by 40%, and maximize productivity.
            </p>

            <div className="hero-actions">
              <button
                type="button"
                className="hero-btn hero-btn-primary"
                onClick={() => navigate('/beta-login')}
              >
                <PlayArrow />
                BETA ACCESS
              </button>
              <button
                type="button"
                className="hero-btn hero-btn-outline"
                onClick={() => navigate('/login')}
              >
                <ArrowForward />
                LIVE ACCESS
              </button>
            </div>
          </div>

          {/* Right Stats Area */}
          <div className="hero-stats-right">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="hero-stat-card"
              >
                <div className="stat-icon">{stat.icon}</div>
                <div className="stat-number">{stat.number}</div>
                <div className="stat-label">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Tabbed Content Section */}
      <section className="tabbed-content" id="features">
        <div className="container">
          <div className="tab-navigation">
            <button
              type="button"
              className={`tab-btn ${activeTab === 'features' ? 'active' : ''}`}
              onClick={() => setActiveTab('features')}
            >
              Features
            </button>
            <button
              type="button"
              className={`tab-btn ${activeTab === 'pricing' ? 'active' : ''}`}
              onClick={() => setActiveTab('pricing')}
            >
              Pricing
            </button>
            <button
              type="button"
              className={`tab-btn ${activeTab === 'about' ? 'active' : ''}`}
              onClick={() => setActiveTab('about')}
            >
              About
            </button>
            <button
              type="button"
              className={`tab-btn ${activeTab === 'contact' ? 'active' : ''}`}
              onClick={() => setActiveTab('contact')}
            >
              Contact
            </button>
          </div>

          <div className="tab-content">
            {activeTab === 'features' && (
              <div className="features-content">
                <h2>Powerful Features for Modern Farmers</h2>
                <div className="features-grid">
                  {features.slice(0, 6).map((feature, index) => (
                    <div key={index} className="feature-card">
                      <div className="feature-icon">{feature.icon}</div>
                      <h3>{feature.title}</h3>
                      <p>{feature.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'pricing' && (
              <div className="pricing-content">
                <h2>Choose Your Perfect Plan</h2>
                <div className="pricing-grid">
                  {subscriptionTiers.map((tier, index) => (
                    <div key={index} className={`pricing-card ${tier.popular ? 'popular' : ''}`}>
                      <h3>{tier.name}</h3>
                      <div className="price">{tier.price}</div>
                      <p>{tier.description}</p>
                      <ul>
                        {tier.features.map((feature, i) => (
                          <li key={i}>{feature}</li>
                        ))}
                      </ul>
                      <button
                        type="button"
                        className="pricing-btn"
                        onClick={tier.buttonAction}
                      >
                        {tier.buttonText}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'about' && (
              <div className="about-content">
                <h2>About AgriIntel</h2>
                <p>South Africa's leading livestock management platform, empowering farmers with AI-driven insights and comprehensive farm management solutions.</p>
                <div className="stats-grid">
                  {stats.map((stat, index) => (
                    <div key={index} className="stat-item">
                      <div className="stat-icon">{stat.icon}</div>
                      <div className="stat-number">{stat.number}</div>
                      <div className="stat-label">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'contact' && (
              <div className="contact-content">
                <h2>Get in Touch</h2>
                <div className="contact-info">
                  <div className="contact-item">
                    <Phone />
                    <span>+27 11 123 4567</span>
                  </div>
                  <div className="contact-item">
                    <Email />
                    <span><EMAIL></span>
                  </div>
                  <div className="contact-item">
                    <LocationOn />
                    <span>Cape Town, South Africa</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

    </div>
  );
};

export default PremiumLandingPage;
