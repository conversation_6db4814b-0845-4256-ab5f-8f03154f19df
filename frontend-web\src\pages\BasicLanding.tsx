import React from 'react';

const BasicLanding: React.FC = () => {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #2E7D32, #4CAF50)',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      padding: '2rem'
    }}>
      {/* Header */}
      <header style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '3rem',
        padding: '1rem',
        background: 'rgba(0,0,0,0.2)',
        borderRadius: '10px'
      }}>
        <h1 style={{ margin: 0, fontSize: '2rem' }}>🌾 AgriIntel</h1>
        <div>
          <a 
            href="/beta-login"
            style={{
              padding: '0.8rem 1.5rem',
              margin: '0 0.5rem',
              background: '#FF9800',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              textDecoration: 'none',
              fontSize: '1rem',
              fontWeight: 'bold',
              display: 'inline-block'
            }}
          >
            BETA ACCESS
          </a>
          <a 
            href="/login"
            style={{
              padding: '0.8rem 1.5rem',
              margin: '0 0.5rem',
              background: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              textDecoration: 'none',
              fontSize: '1rem',
              fontWeight: 'bold',
              display: 'inline-block'
            }}
          >
            LIVE ACCESS
          </a>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ textAlign: 'center', maxWidth: '1200px', margin: '0 auto' }}>
        <h2 style={{ fontSize: '3rem', marginBottom: '1rem' }}>
          Smart Livestock Management for South Africa
        </h2>
        
        <p style={{ fontSize: '1.5rem', marginBottom: '3rem', opacity: 0.9 }}>
          Join 15,000+ farmers who've transformed their operations with AI technology
        </p>

        {/* Action Buttons */}
        <div style={{ marginBottom: '4rem' }}>
          <a
            href="/beta-login"
            style={{
              padding: '1.5rem 3rem',
              margin: '1rem',
              background: '#FF9800',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              textDecoration: 'none',
              fontSize: '1.3rem',
              fontWeight: 'bold',
              display: 'inline-block'
            }}
          >
            🚀 START FREE BETA
          </a>
          <a
            href="/login"
            style={{
              padding: '1.5rem 3rem',
              margin: '1rem',
              background: 'transparent',
              color: 'white',
              border: '2px solid white',
              borderRadius: '10px',
              textDecoration: 'none',
              fontSize: '1.3rem',
              fontWeight: 'bold',
              display: 'inline-block'
            }}
          >
            ⚡ GO LIVE
          </a>
        </div>

        {/* Features Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '2rem',
          marginBottom: '4rem'
        }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '2rem',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🐄</div>
            <h3>Smart Livestock Tracking</h3>
            <p>Real-time monitoring of cattle, sheep, and goats with RFID technology</p>
          </div>
          
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '2rem',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🩺</div>
            <h3>AI Health Management</h3>
            <p>Predictive health analytics to prevent diseases and reduce costs</p>
          </div>
          
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '2rem',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
            <h3>Advanced Analytics</h3>
            <p>Comprehensive insights and reporting for better farm management</p>
          </div>
        </div>

        {/* Stats */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '2rem',
          marginBottom: '4rem'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#FFD700' }}>15K+</div>
            <div>South African Farmers</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#FFD700' }}>750K+</div>
            <div>Livestock Tracked</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#FFD700' }}>99.9%</div>
            <div>System Uptime</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#FFD700' }}>40%</div>
            <div>Cost Reduction</div>
          </div>
        </div>

        {/* Pricing */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '2rem',
          marginBottom: '4rem'
        }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '2rem',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <h3>Beta Access</h3>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#FFD700' }}>FREE</div>
            <p>Perfect for small-scale farmers</p>
            <ul style={{ listStyle: 'none', padding: 0 }}>
              <li>✅ Up to 50 animals</li>
              <li>✅ Basic health monitoring</li>
              <li>✅ Mobile app access</li>
            </ul>
            <a
              href="/beta-login"
              style={{
                width: '80%',
                padding: '1rem',
                background: '#FF9800',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                textDecoration: 'none',
                fontWeight: 'bold',
                display: 'inline-block',
                marginTop: '1rem'
              }}
            >
              START BETA
            </a>
          </div>
          
          <div style={{
            background: 'rgba(255,255,255,0.15)',
            padding: '2rem',
            borderRadius: '10px',
            textAlign: 'center',
            border: '2px solid #FFD700'
          }}>
            <h3>Professional</h3>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#FFD700' }}>R299</div>
            <p>For growing commercial farms</p>
            <ul style={{ listStyle: 'none', padding: 0 }}>
              <li>✅ Up to 500 animals</li>
              <li>✅ AI health analytics</li>
              <li>✅ Financial management</li>
              <li>✅ Priority support</li>
            </ul>
            <a
              href="/login"
              style={{
                width: '80%',
                padding: '1rem',
                background: '#2196F3',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                textDecoration: 'none',
                fontWeight: 'bold',
                display: 'inline-block',
                marginTop: '1rem'
              }}
            >
              GO LIVE
            </a>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer style={{
        textAlign: 'center',
        padding: '2rem',
        background: 'rgba(0,0,0,0.2)',
        borderRadius: '10px',
        marginTop: '3rem'
      }}>
        <h3>🌾 AgriIntel</h3>
        <p>Smart Livestock Management for South African Farmers</p>
        <div style={{ marginTop: '1rem' }}>
          <span style={{ margin: '0 1rem' }}>📞 +27 11 123 4567</span>
          <span style={{ margin: '0 1rem' }}>📧 <EMAIL></span>
          <span style={{ margin: '0 1rem' }}>📍 Cape Town, South Africa</span>
        </div>
        <p style={{ marginTop: '1rem', opacity: 0.7 }}>
          © 2024 AgriIntel. All rights reserved. Made with ❤️ for South African farmers.
        </p>
      </footer>
    </div>
  );
};

export default BasicLanding;
