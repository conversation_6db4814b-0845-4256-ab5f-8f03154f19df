const express = require('express');
const router = express.Router();
const controller = require('../controllers/mongoDbController');

// Define routes
router.get('/', (req, res) => {
  res.status(200).json({ message: 'Health records endpoint' });
});

// Health records routes
router.get('/records', controller.getHealthRecords);
router.get('/vaccinations', controller.getVaccinations);

// CRUD operations
router.post('/records', controller.createHealthRecord);
router.put('/records/:id', controller.updateHealthRecord);
router.delete('/records/:id', controller.deleteHealthRecord);
  }

  return router;
};