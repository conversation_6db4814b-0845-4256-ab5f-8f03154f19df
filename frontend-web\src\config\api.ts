// API configuration
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
export const API_URL = API_BASE_URL; // Alias for backward compatibility

// MongoDB connection should only be handled on the server side
// Frontend should never directly connect to MongoDB
// All database operations should go through the API

// Standard API routes
export const API_ROUTES = {
  // Animal Management
  animals: {
    getAll: () => `${API_URL}/animals`,
    getById: (id) => `${API_URL}/animals/${id}`,
    create: () => `${API_URL}/animals`,
    update: (id) => `${API_URL}/animals/${id}`,
    delete: (id) => `${API_URL}/animals/${id}`
  },

  // User Management
  users: {
    getAll: () => `${API_URL}/users`,
    getById: (id) => `${API_URL}/users/${id}`,
    create: () => `${API_URL}/users`,
    update: (id) => `${API_URL}/users/${id}`,
    delete: (id) => `${API_URL}/users/${id}`
  },

  // Feeding Management
  feeding: {
    getAll: () => `${API_URL}/feeding`,
    getById: (id) => `${API_URL}/feeding/${id}`,
    create: () => `${API_URL}/feeding`,
    update: (id) => `${API_URL}/feeding/${id}`,
    delete: (id) => `${API_URL}/feeding/${id}`
  },

  // Health Management
  health: {
    getAll: () => `${API_URL}/health`,
    getById: (id) => `${API_URL}/health/${id}`,
    create: () => `${API_URL}/health`,
    update: (id) => `${API_URL}/health/${id}`,
    delete: (id) => `${API_URL}/health/${id}`
  },

  // Breeding Management
  breeding: {
    getAll: () => `${API_URL}/breeding`,
    getById: (id) => `${API_URL}/breeding/${id}`,
    create: () => `${API_URL}/breeding`,
    update: (id) => `${API_URL}/breeding/${id}`,
    delete: (id) => `${API_URL}/breeding/${id}`
  },

  // Financial Management
  financial: {
    getAll: () => `${API_URL}/financial`,
    getById: (id) => `${API_URL}/financial/${id}`,
    create: () => `${API_URL}/financial`,
    update: (id) => `${API_URL}/financial/${id}`,
    delete: (id) => `${API_URL}/financial/${id}`
  },

  // Inventory Management
  inventory: {
    getAll: () => `${API_URL}/inventory`,
    getById: (id) => `${API_URL}/inventory/${id}`,
    create: () => `${API_URL}/inventory`,
    update: (id) => `${API_URL}/inventory/${id}`,
    delete: (id) => `${API_URL}/inventory/${id}`
  },

  // Reports
  reports: {
    getAll: () => `${API_URL}/reports`,
    getById: (id) => `${API_URL}/reports/${id}`,
    create: () => `${API_URL}/reports`,
    update: (id) => `${API_URL}/reports/${id}`,
    delete: (id) => `${API_URL}/reports/${id}`
  },

  // Settings
  settings: {
    getAll: () => `${API_URL}/settings`,
    getById: (id) => `${API_URL}/settings/${id}`,
    create: () => `${API_URL}/settings`,
    update: (id) => `${API_URL}/settings/${id}`,
    delete: (id) => `${API_URL}/settings/${id}`
  },

  // Business Analysis
  business: {
    getAll: () => `${API_URL}/business`,
    getById: (id) => `${API_URL}/business/${id}`,
    create: () => `${API_URL}/business`,
    update: (id) => `${API_URL}/business/${id}`,
    delete: (id) => `${API_URL}/business/${id}`
  }
};
