import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  alpha,
  Divider,
  Chip,
  Container
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import {
  Download,
  FilterList,
  Assessment,
  Pie<PERSON>hart as PieChartIcon,
  <PERSON><PERSON><PERSON> as BarChartIcon,
  Timeline,
  Print,
  Share,
  Refresh,
  AttachMoney,
  Pets,
  LocalHospital,
  Restaurant,
  TrendingUp,
  Warning,
  Analytics,
  BusinessCenter,
  MonetizationOn,
  Lightbulb,
  ShowChart,
  Insights
} from '@mui/icons-material';
import StrategyIcon from '@mui/icons-material/AutoGraph';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';
import { useThemeContext } from '../../contexts/ThemeContext';
import { StandardDashboard, AnimatedBackgroundCard, EnhancedChart } from '../../components/common';
import { useAnimalData } from '../../hooks/useAnimalData';
import { useFinancialData } from '../../hooks/useFinancialData';
import { useHealthRecords } from '../../hooks/useHealthRecords';
import { useBreedingRecords } from '../../hooks/useBreedingRecords';
import { useFeedingData } from '../../hooks/useFeedingData';
import { useBusinessStrategy } from '../../hooks/useBusinessStrategy';
import { useBusinessPredictions } from '../../hooks/useBusinessPredictions';

// Import custom components
import BusinessAnalyticsButton from '../../components/analytics/BusinessAnalyticsButton';
import BusinessAnalyticsCard from '../../components/analytics/BusinessAnalyticsCard';
import BusinessMetricsCard from '../../components/analytics/BusinessMetricsCard';
import BusinessPredictionCard from '../../components/analytics/BusinessPredictionCard';
import BusinessStrategyCard from '../../components/analytics/BusinessStrategyCard';
import BusinessInsightCard from '../../components/analytics/BusinessInsightCard';

// Mock data for charts
// This will be populated with real data in the component
const revenueData = [
  { month: 'Jan', revenue: 25000, expenses: 18000, profit: 7000 },
  { month: 'Feb', revenue: 30000, expenses: 20000, profit: 10000 },
  { month: 'Mar', revenue: 28000, expenses: 19000, profit: 9000 },
  { month: 'Apr', revenue: 35000, expenses: 22000, profit: 13000 },
  { month: 'May', revenue: 40000, expenses: 25000, profit: 15000 },
  { month: 'Jun', revenue: 45000, expenses: 28000, profit: 17000 },
];

// This will be populated with real data in the component
const animalDistributionData = [
  { name: 'Cattle', value: 120, color: '#8884d8' },
  { name: 'Sheep', value: 200, color: '#82ca9d' },
  { name: 'Goats', value: 80, color: '#ffc658' },
  { name: 'Pigs', value: 40, color: '#ff8042' },
];

const healthData = [
  { month: 'Jan', vaccinations: 45, treatments: 12, checkups: 30 },
  { month: 'Feb', vaccinations: 50, treatments: 15, checkups: 35 },
  { month: 'Mar', vaccinations: 40, treatments: 20, checkups: 28 },
  { month: 'Apr', vaccinations: 55, treatments: 10, checkups: 40 },
  { month: 'May', vaccinations: 60, treatments: 8, checkups: 45 },
  { month: 'Jun', vaccinations: 48, treatments: 14, checkups: 38 },
];

const breedingData = [
  { month: 'Jan', success: 75, attempts: 100 },
  { month: 'Feb', success: 80, attempts: 100 },
  { month: 'Mar', success: 70, attempts: 100 },
  { month: 'Apr', success: 85, attempts: 100 },
  { month: 'May', success: 90, attempts: 100 },
  { month: 'Jun', success: 82, attempts: 100 },
];

const feedData = [
  { month: 'Jan', hay: 2000, grain: 1500, supplements: 500 },
  { month: 'Feb', hay: 2200, grain: 1600, supplements: 550 },
  { month: 'Mar', hay: 2100, grain: 1550, supplements: 525 },
  { month: 'Apr', hay: 2300, grain: 1650, supplements: 575 },
  { month: 'May', hay: 2400, grain: 1700, supplements: 600 },
  { month: 'Jun', hay: 2250, grain: 1625, supplements: 560 },
];

const growthData = [
  { month: 'Jan', cattle: 5.2, sheep: 3.1, goats: 2.8 },
  { month: 'Feb', cattle: 5.5, sheep: 3.3, goats: 3.0 },
  { month: 'Mar', cattle: 5.3, sheep: 3.2, goats: 2.9 },
  { month: 'Apr', cattle: 5.7, sheep: 3.4, goats: 3.1 },
  { month: 'May', cattle: 6.0, sheep: 3.6, goats: 3.3 },
  { month: 'Jun', cattle: 5.8, sheep: 3.5, goats: 3.2 },
];

// This will be populated with real data in the component
const kpiData = [
  { name: 'Revenue', value: 'R 203,000', trend: '+15%', status: 'positive' },
  { name: 'Expenses', value: 'R 132,000', trend: '+8%', status: 'warning' },
  { name: 'Profit Margin', value: '35%', trend: '+5%', status: 'positive' },
  { name: 'ROI', value: '22%', trend: '+3%', status: 'positive' },
  { name: 'Animal Count', value: '440', trend: '+10%', status: 'positive' },
  { name: 'Feed Efficiency', value: '1.8', trend: '-0.2', status: 'positive' },
  { name: 'Breeding Success', value: '82%', trend: '+4%', status: 'positive' },
  { name: 'Health Incidents', value: '12', trend: '-25%', status: 'positive' },
];

const BusinessAnalysisDashboard: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const { themeColor } = useThemeContext();
  const [timeRange, setTimeRange] = useState('6months');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Data hooks
  const { animals, loading: animalsLoading } = useAnimalData();
  const { transactions, loading: financialLoading } = useFinancialData();
  const { healthRecords, loading: healthLoading } = useHealthRecords();
  const { breedingRecords, loading: breedingLoading } = useBreedingRecords();
  const { feedingRecords, loading: feedingLoading } = useFeedingData();

  // Dashboard stats based on actual data
  const dashboardStats = [
    {
      label: translate('dashboard.total_revenue'),
      value: financialLoading ? '...' : `R ${transactions?.reduce((sum, t) => sum + (t.amount || 0), 0).toLocaleString() || '0'}`,
      change: '+12%',
      trend: 'up'
    },
    {
      label: translate('dashboard.profit_margin'),
      value: financialLoading ? '...' : '32%',
      change: '+5%',
      trend: 'up'
    },
    {
      label: translate('dashboard.total_animals'),
      value: animalsLoading ? '...' : `${animals?.length.toLocaleString() || '0'}`,
      change: '+8%',
      trend: 'up'
    },
    {
      label: translate('dashboard.health_index'),
      value: healthLoading ? '...' : `${Math.round((1 - (healthRecords?.filter(r => r.status === 'sick').length || 0) / (animals?.length || 1)) * 100)}%`,
      change: '+2%',
      trend: 'up'
    }
  ];

  // Business strategy and predictions hooks
  const { strategyData, loading: strategyLoading } = useBusinessStrategy();
  const { predictions, insights, loading: predictionsLoading } = useBusinessPredictions();

  // Generate animal distribution data from actual animals
  const realAnimalDistributionData = React.useMemo(() => {
    if (animalsLoading || !animals) return animalDistributionData;

    // Count animals by species
    const speciesCounts: Record<string, number> = {};
    const colors = {
      'Cattle': '#8884d8',
      'Sheep': '#82ca9d',
      'Goats': '#ffc658',
      'Pigs': '#ff8042',
      'Other': '#d88484'
    };

    animals.forEach(animal => {
      const species = animal.species || 'Other';
      speciesCounts[species] = (speciesCounts[species] || 0) + 1;
    });

    // Convert to array format for chart
    return Object.entries(speciesCounts).map(([name, value]) => ({
      name,
      value,
      color: colors[name as keyof typeof colors] || colors['Other']
    }));
  }, [animals, animalsLoading]);

  // Generate revenue data from actual transactions
  const realRevenueData = React.useMemo(() => {
    if (financialLoading || !transactions) return revenueData;

    // Group transactions by month
    const monthlyData: Record<string, { revenue: number, expenses: number }> = {};
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Initialize all months with zero values
    months.forEach(month => {
      monthlyData[month] = { revenue: 0, expenses: 0 };
    });

    // Aggregate transactions by month
    transactions.forEach(transaction => {
      if (!transaction.date) return;

      const date = new Date(transaction.date);
      const month = months[date.getMonth()];

      if (transaction.type === 'income') {
        monthlyData[month].revenue += transaction.amount || 0;
      } else if (transaction.type === 'expense') {
        monthlyData[month].expenses += transaction.amount || 0;
      }
    });

    // Convert to array format for chart and calculate profit
    return months.map(month => ({
      month,
      revenue: monthlyData[month].revenue,
      expenses: monthlyData[month].expenses,
      profit: monthlyData[month].revenue - monthlyData[month].expenses
    }));
  }, [transactions, financialLoading]);

  // Generate KPI data from actual data
  const realKpiData = React.useMemo(() => {
    // Calculate revenue
    const totalRevenue = transactions?.reduce((sum, t) =>
      t.type === 'income' ? sum + (t.amount || 0) : sum, 0) || 0;

    // Calculate expenses
    const totalExpenses = transactions?.reduce((sum, t) =>
      t.type === 'expense' ? sum + (t.amount || 0) : sum, 0) || 0;

    // Calculate profit margin
    const profitMargin = totalRevenue > 0
      ? Math.round((totalRevenue - totalExpenses) / totalRevenue * 100)
      : 0;

    // Calculate ROI
    const roi = totalExpenses > 0
      ? Math.round((totalRevenue - totalExpenses) / totalExpenses * 100)
      : 0;

    // Calculate animal count
    const animalCount = animals?.length || 0;

    // Calculate health incidents
    const healthIncidents = healthRecords?.filter(r =>
      r.status === 'sick' || r.status === 'treatment').length || 0;

    // Calculate breeding success
    const breedingAttempts = breedingRecords?.length || 0;
    const breedingSuccess = breedingRecords?.filter(r =>
      r.status === 'success' || r.status === 'pregnant').length || 0;
    const breedingSuccessRate = breedingAttempts > 0
      ? Math.round((breedingSuccess / breedingAttempts) * 100)
      : 0;

    // Calculate feed efficiency (simplified)
    const feedEfficiency = 1.8; // This would normally be calculated from actual data

    return [
      {
        name: 'Revenue',
        value: `R ${totalRevenue.toLocaleString()}`,
        trend: '+15%',
        status: 'positive'
      },
      {
        name: 'Expenses',
        value: `R ${totalExpenses.toLocaleString()}`,
        trend: '+8%',
        status: 'warning'
      },
      {
        name: 'Profit Margin',
        value: `${profitMargin}%`,
        trend: '+5%',
        status: 'positive'
      },
      {
        name: 'ROI',
        value: `${roi}%`,
        trend: '+3%',
        status: 'positive'
      },
      {
        name: 'Animal Count',
        value: animalCount.toString(),
        trend: '+10%',
        status: 'positive'
      },
      {
        name: 'Feed Efficiency',
        value: feedEfficiency.toString(),
        trend: '-0.2',
        status: 'positive'
      },
      {
        name: 'Breeding Success',
        value: `${breedingSuccessRate}%`,
        trend: '+4%',
        status: 'positive'
      },
      {
        name: 'Health Incidents',
        value: healthIncidents.toString(),
        trend: '-25%',
        status: 'positive'
      },
    ];
  }, [animals, transactions, healthRecords, breedingRecords]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  // Handle time range change
  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    setTimeRange(event.target.value);
  };

  // Handle refresh data
  const handleRefreshData = useCallback(() => {
    // Don't set loading state at all - this prevents the blinking issue
    // Just let the data refresh in the background
  }, []);

  // Handle tab change
  const handleTabChange = useCallback((index: number) => {
    setActiveTab(index);
  }, []);

  // We don't need an initial loading state anymore
  // The component will render immediately with whatever data is available

  // Dashboard actions
  const dashboardActions = [
    {
      label: translate('dashboard.refresh'),
      icon: <Refresh />,
      onClick: handleRefreshData,
      color: theme.palette.primary.main
    },
    {
      label: translate('dashboard.strategy'),
      icon: <StrategyIcon />,
      onClick: () => window.location.href = '/dashboard/analytics/strategy',
      color: theme.palette.secondary.main
    },
    {
      label: translate('dashboard.predictions'),
      icon: <TrendingUp />,
      onClick: () => window.location.href = '/dashboard/analytics/predictions',
      color: theme.palette.info.main
    },
    {
      label: translate('dashboard.download'),
      icon: <Download />,
      onClick: () => console.log('Download report'),
      color: theme.palette.success.main
    }
  ];

  // Dashboard tabs
  const dashboardTabs = [
    {
      label: translate('dashboard.overview'),
      icon: <Assessment />,
      content: (
        <Box>
          {/* KPI Summary */}
          <Grid container spacing={3} mb={4}>
            {realKpiData.map((kpi, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <motion.div variants={itemVariants}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 3,
                      borderRadius: 2,
                      height: '100%',
                      background: alpha(theme.palette.background.paper, 0.8),
                      backdropFilter: 'blur(10px)',
                      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: `0 10px 30px ${alpha(theme.palette.primary.main, 0.1)}`
                      }
                    }}
                  >
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      {kpi.name}
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" gutterBottom>
                      {kpi.value}
                    </Typography>
                    <Chip
                      label={kpi.trend}
                      size="small"
                      color={kpi.status === 'positive' ? 'success' : kpi.status === 'warning' ? 'warning' : 'error'}
                      sx={{ fontWeight: 'bold' }}
                    />
                  </Paper>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Financial Performance */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={8}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Financial Performance"
                  icon={<AttachMoney />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.1}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={realRevenueData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis tickFormatter={(value) => `R${value / 1000}k`} />
                        <Tooltip formatter={(value) => `R${value.toLocaleString()}`} />
                        <Legend />
                        <Bar dataKey="revenue" name="Revenue" fill={theme.palette.primary.main} />
                        <Bar dataKey="expenses" name="Expenses" fill={theme.palette.error.main} />
                        <Bar dataKey="profit" name="Profit" fill={theme.palette.success.main} />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Animal Distribution"
                  icon={<Pets />}
                  accentColor={theme.palette.secondary.main}
                  secondaryColor={theme.palette.secondary.dark}
                  delay={0.2}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={realAnimalDistributionData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {realAnimalDistributionData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => [`${value} animals`, 'Count']} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>
          </Grid>

          {/* Health and Breeding */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Health Statistics"
                  icon={<LocalHospital />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  delay={0.3}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={healthData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="vaccinations" name="Vaccinations" stroke={theme.palette.primary.main} activeDot={{ r: 8 }} />
                        <Line type="monotone" dataKey="treatments" name="Treatments" stroke={theme.palette.error.main} />
                        <Line type="monotone" dataKey="checkups" name="Checkups" stroke={theme.palette.info.main} />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Breeding Success Rate"
                  icon={<TrendingUp />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  delay={0.4}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={breedingData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Area type="monotone" dataKey="success" name="Success Rate (%)" stroke={theme.palette.success.main} fill={alpha(theme.palette.success.main, 0.2)} />
                        <Area type="monotone" dataKey="attempts" name="Total Attempts" stroke={theme.palette.info.main} fill={alpha(theme.palette.info.main, 0.2)} />
                      </AreaChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>
          </Grid>

          {/* Feed and Growth */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Feed Consumption"
                  icon={<Restaurant />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  delay={0.5}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={feedData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis tickFormatter={(value) => `${value}kg`} />
                        <Tooltip formatter={(value) => `${value}kg`} />
                        <Legend />
                        <Bar dataKey="hay" name="Hay" fill={theme.palette.success.light} />
                        <Bar dataKey="grain" name="Grain" fill={theme.palette.warning.light} />
                        <Bar dataKey="supplements" name="Supplements" fill={theme.palette.info.light} />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Growth Rates"
                  icon={<TrendingUp />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.6}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={growthData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis tickFormatter={(value) => `${value}kg`} />
                        <Tooltip formatter={(value) => `${value}kg/week`} />
                        <Legend />
                        <Line type="monotone" dataKey="cattle" name="Cattle" stroke={theme.palette.primary.main} activeDot={{ r: 8 }} />
                        <Line type="monotone" dataKey="sheep" name="Sheep" stroke={theme.palette.secondary.main} />
                        <Line type="monotone" dataKey="goats" name="Goats" stroke={theme.palette.warning.main} />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>
          </Grid>
        </Box>
      )
    },
    {
      label: translate('dashboard.financial'),
      icon: <AttachMoney />,
      content: (
        <Box>
          <Typography variant="h6" gutterBottom>
            Financial Analysis
          </Typography>
          <EnhancedChart
            title="Revenue vs Expenses"
            subtitle="Monthly comparison"
            data={realRevenueData}
            type="bar"
            dataKeys={['revenue', 'expenses', 'profit']}
            xAxisDataKey="month"
            height={400}
            accentColor={theme.palette.primary.main}
            allowChartTypeChange={true}
            allowTimeRangeChange={true}
            module="financial"
            onRefresh={handleRefreshData}
          />
        </Box>
      )
    },
    {
      label: translate('dashboard.animals'),
      icon: <Pets />,
      content: (
        <Box>
          <Typography variant="h6" gutterBottom>
            Animal Analysis
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <EnhancedChart
                title="Animal Distribution"
                subtitle="By species"
                data={realAnimalDistributionData}
                type="pie"
                dataKeys={['value']}
                xAxisDataKey="name"
                height={400}
                accentColor={theme.palette.secondary.main}
                module="animals"
                onRefresh={handleRefreshData}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <EnhancedChart
                title="Growth Rates"
                subtitle="Monthly average"
                data={growthData}
                type="line"
                dataKeys={['cattle', 'sheep', 'goats']}
                xAxisDataKey="month"
                height={400}
                accentColor={theme.palette.primary.main}
                module="animals"
                onRefresh={handleRefreshData}
              />
            </Grid>
          </Grid>
        </Box>
      )
    },
    {
      label: translate('dashboard.operations'),
      icon: <Assessment />,
      content: (
        <Box>
          <Typography variant="h6" gutterBottom>
            Operational Analysis
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <EnhancedChart
                title="Feed Consumption"
                subtitle="Monthly usage"
                data={feedData}
                type="bar"
                dataKeys={['hay', 'grain', 'supplements']}
                xAxisDataKey="month"
                height={400}
                accentColor={theme.palette.warning.main}
                module="feeding"
                onRefresh={handleRefreshData}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <EnhancedChart
                title="Health Statistics"
                subtitle="Monthly activities"
                data={healthData}
                type="line"
                dataKeys={['vaccinations', 'treatments', 'checkups']}
                xAxisDataKey="month"
                height={400}
                accentColor={theme.palette.success.main}
                module="health"
                onRefresh={handleRefreshData}
              />
            </Grid>
          </Grid>
        </Box>
      )
    },
    {
      label: translate('dashboard.strategy'),
      icon: <StrategyIcon />,
      content: (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              {translate('dashboard.business_strategy')}
            </Typography>
            <BusinessAnalyticsButton
              variant="contained"
              color="primary"
              onClick={() => window.location.href = '/analytics/strategy'}
              startIcon={<StrategyIcon />}
            >
              {translate('dashboard.view_full_strategy')}
            </BusinessAnalyticsButton>
          </Box>

          {/* Vision and Mission */}
          <BusinessAnalyticsCard
            title={translate('dashboard.vision_mission')}
            icon={<BusinessCenter />}
            delay={0.1}
          >
            <Box p={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                {translate('dashboard.vision')}
              </Typography>
              <Typography variant="body1" paragraph>
                {strategyData.vision}
              </Typography>

              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                {translate('dashboard.mission')}
              </Typography>
              <Typography variant="body1">
                {strategyData.mission}
              </Typography>
            </Box>
          </BusinessAnalyticsCard>

          <Box mt={4}>
            <Typography variant="h6" gutterBottom>
              {translate('dashboard.strategic_opportunities')}
            </Typography>

            <Grid container spacing={3}>
              {strategyData.opportunities.slice(0, 2).map((opportunity, index) => (
                <Grid item xs={12} md={6} key={opportunity.id}>
                  <BusinessStrategyCard
                    strategy={{
                      id: opportunity.id,
                      name: opportunity.name,
                      description: opportunity.description,
                      potentialValue: opportunity.potentialValue,
                      implementationTimeframe: '6-12 months',
                      implementationStrategy: opportunity.implementationPlan,
                      feasibility: opportunity.feasibility,
                      opportunityScore: opportunity.opportunityScore,
                      tags: ['Strategy', 'Growth', 'Innovation'],
                      category: 'Strategic'
                    }}
                    icon={<StrategyIcon />}
                    delay={0.2 + (index * 0.1)}
                  />
                </Grid>
              ))}
            </Grid>
          </Box>

          <Box mt={4}>
            <Typography variant="h6" gutterBottom>
              {translate('dashboard.strategic_goals')}
            </Typography>

            <Grid container spacing={3}>
              {strategyData.goals.slice(0, 4).map((goal, index) => (
                <Grid item xs={12} md={6} key={goal.id}>
                  <BusinessAnalyticsCard
                    title={goal.name}
                    subtitle={`Target: ${goal.targetDate}`}
                    icon={<TrendingUp />}
                    delay={0.3 + (index * 0.1)}
                  >
                    <Box p={2}>
                      <Typography variant="body1" paragraph>
                        {goal.description}
                      </Typography>

                      <Box sx={{ mt: 2, mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            Progress
                          </Typography>
                          <Typography variant="body2" fontWeight="medium">
                            {goal.progress}%
                          </Typography>
                        </Box>
                        <Box sx={{
                          width: '100%',
                          height: 8,
                          backgroundColor: alpha(theme.palette.primary.main, 0.2),
                          borderRadius: 4
                        }}>
                          <Box sx={{
                            width: `${goal.progress}%`,
                            height: '100%',
                            backgroundColor: goal.status === 'on-track'
                              ? theme.palette.success.main
                              : goal.status === 'at-risk'
                                ? theme.palette.warning.main
                                : theme.palette.error.main,
                            borderRadius: 4
                          }} />
                        </Box>
                      </Box>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                        <Chip
                          label={goal.status}
                          size="small"
                          color={
                            goal.status === 'on-track'
                              ? 'success'
                              : goal.status === 'at-risk'
                                ? 'warning'
                                : 'error'
                          }
                        />
                        <Typography variant="body2" color="text.secondary">
                          Owner: {goal.owner}
                        </Typography>
                      </Box>
                    </Box>
                  </BusinessAnalyticsCard>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Box>
      )
    },
    {
      label: translate('dashboard.predictions'),
      icon: <TrendingUp />,
      content: (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              {translate('dashboard.predictive_insights')}
            </Typography>
            <BusinessAnalyticsButton
              variant="contained"
              color="primary"
              onClick={() => window.location.href = '/analytics/predictions'}
              startIcon={<TrendingUp />}
            >
              {translate('dashboard.view_all_predictions')}
            </BusinessAnalyticsButton>
          </Box>

          <Grid container spacing={3}>
            {predictions.map((prediction, index) => (
              <Grid item xs={12} md={6} key={prediction.id}>
                <BusinessPredictionCard
                  prediction={prediction}
                  icon={<TrendingUp />}
                  delay={0.1 + (index * 0.1)}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      )
    },
    {
      label: translate('dashboard.insights'),
      icon: <Lightbulb />,
      content: (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              {translate('dashboard.business_insights')}
            </Typography>
            <BusinessAnalyticsButton
              variant="contained"
              color="primary"
              onClick={() => window.location.href = '/analytics/predictions'}
              startIcon={<Lightbulb />}
            >
              {translate('dashboard.view_all_insights')}
            </BusinessAnalyticsButton>
          </Box>

          <Grid container spacing={3}>
            {insights.map((insight, index) => (
              <Grid item xs={12} md={6} key={insight.id}>
                <BusinessInsightCard
                  insight={insight}
                  delay={0.1 + (index * 0.1)}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      )
    }
  ];

  return (
    <StandardDashboard
      title={translate('dashboard.business_analysis')}
      subtitle={translate('dashboard.business_analysis_description')}
      icon={<Analytics />}
      stats={dashboardStats.map(stat => ({
        label: stat.label,
        value: stat.value,
        trend: {
          value: parseInt(stat.change) || 0,
          isPositive: stat.trend === 'up'
        }
      }))}
      actions={dashboardActions}
      tabs={dashboardTabs}
      activeTab={activeTab}
      onTabChange={handleTabChange}
      isLoading={false}
      loadingMessage={translate('dashboard.loading_analysis')}
      onRefresh={handleRefreshData}
      module="analytics"
    />
  );
};

export default BusinessAnalysisDashboard;
