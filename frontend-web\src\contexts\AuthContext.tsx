import { createContext, useState, useContext, useEffect } from 'react';
import { authAPI } from '../services/api';
import { User } from '../types/user';

interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
  hasPermission: (permission: string) => boolean;
  isBetaUser: () => boolean;
  isEnterpriseUser: () => boolean;
  isAdminUser: () => boolean;
  canAccessModule: (module: string) => boolean;
  getAccessLevel: () => 'beta' | 'professional' | 'enterprise' | 'admin';
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check for existing user session on mount
  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsLoading(true);
        console.log('Initializing authentication');

        // Try to get user from localStorage first for immediate UI rendering
        const savedUser = localStorage.getItem('user');
        const savedToken = localStorage.getItem('token');

        if (savedUser && savedToken) {
          console.log('Found saved user in localStorage');
          const parsedUser = JSON.parse(savedUser);
          setUser(parsedUser);

          // Check token expiration
          try {
            const tokenData = JSON.parse(atob(savedToken.split('.')[1]));
            const expirationTime = tokenData.exp * 1000; // Convert to milliseconds
            const currentTime = Date.now();

            if (expirationTime < currentTime) {
              console.log('Token expired, clearing local data');
              localStorage.removeItem('user');
              localStorage.removeItem('token');
              setUser(null);
              setError('Your session has expired. Please log in again.');
              setIsLoading(false);
              return;
            }

            console.log('Token valid until:', new Date(expirationTime).toLocaleString());
          } catch (tokenError) {
            console.error('Error parsing token:', tokenError);
          }
        } else {
          console.log('No saved user found in localStorage');
        }

        // Skip server verification if we're on the login page
        if (window.location.pathname === '/login') {
          console.log('On login page, skipping server verification');
          setIsLoading(false);
          return;
        }

        // Then verify with the server/service only if we have a token
        if (savedToken) {
          try {
            console.log('Verifying user with server');
            const response = await authAPI.getCurrentUser();

            if (response.success) {
              console.log('Server verified user successfully');
              setUser(response.data);
              localStorage.setItem('user', JSON.stringify(response.data));
            } else if (savedUser) {
              console.log('Server did not recognize user, clearing local data');
              // If server doesn't recognize the user but we have local data, clear it
              localStorage.removeItem('user');
              localStorage.removeItem('token');
              setUser(null);
            }
          } catch (error: any) {
            console.error('Error verifying user with server:', error);

            if (error.status === 401 || error.status === 403) {
              console.log('Authentication error, clearing local data');
              // If API call fails with auth error, clear local storage
              localStorage.removeItem('user');
              localStorage.removeItem('token');
              setUser(null);

              // Redirect to login page if not already there
              if (window.location.pathname !== '/login') {
                window.location.href = '/login';
              }
            } else {
              console.log('Network or server error, keeping local data for offline access');
              // For other errors (like network issues), keep the local user data
              // This allows the app to work offline with the last known user
            }
          }
        }
      } catch (err) {
        console.error('Auth initialization error:', err);
        setError('Failed to initialize authentication');
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);

      console.log(`Attempting to login with username: ${username}`);

      // HARDCODED CREDENTIALS FOR PRODUCTION
      if (username === 'Demo' && password === '123') {
        console.log('Demo user login - BETA access');
        const demoUser = {
          id: 'demo-user-id',
          username: 'Demo',
          email: '<EMAIL>',
          role: 'beta',
          permissions: ['view_animals', 'create_animal', 'view_health_records', 'view_resources'],
          firstName: 'Demo',
          lastName: 'User',
          isActive: true,
          lastLogin: new Date().toISOString()
        };

        const demoResponse = {
          success: true,
          user: demoUser,
          token: 'demo-token-' + Date.now(),
          message: 'Demo login successful'
        };

        handleSuccessfulLogin(demoResponse);
        return;
      }

      if (username === 'admin' && password === 'Admin@123') {
        console.log('Admin user login - FULL access');
        const adminUser = {
          id: 'admin-user-id',
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          permissions: ['all'],
          firstName: 'Administrator',
          lastName: 'User',
          isActive: true,
          lastLogin: new Date().toISOString()
        };

        const adminResponse = {
          success: true,
          user: adminUser,
          token: 'admin-token-' + Date.now(),
          message: 'Admin login successful'
        };

        handleSuccessfulLogin(adminResponse);
        return;
      }

      if ((username === 'May Rakgama' || username === 'may.rakgama' || username === 'may') && password === 'MayAdmin@2024') {
        console.log('May Rakgama login - SUPER ADMIN access');
        const mayUser = {
          id: 'may-rakgama-id',
          username: 'May Rakgama',
          email: '<EMAIL>',
          role: 'admin',
          permissions: ['all'],
          firstName: 'May',
          lastName: 'Rakgama',
          isActive: true,
          lastLogin: new Date().toISOString()
        };

        const mayResponse = {
          success: true,
          user: mayUser,
          token: 'may-token-' + Date.now(),
          message: 'May Rakgama login successful'
        };

        handleSuccessfulLogin(mayResponse);
        return;
      }

      // Regular API login for other users
      console.log('Attempting API login for user:', username);
      const response = await authAPI.login(username, password);

      if (response.success) {
        console.log('API login successful');
        const userData = response.data;
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('token', userData.token);
        return userData;
      } else {
        console.log('API login failed:', response.message);
        setError(response.message || 'Invalid username or password');
        throw new Error(response.message || 'Invalid credentials');
      }
    } catch (err: any) {
      console.error('Login error (outer catch):', err);

      // Clear any partial data
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      setUser(null);

      // Make sure isLoading is set to false
      setIsLoading(false);

      throw err; // Re-throw to handle in the component
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to handle successful login
  const handleSuccessfulLogin = (response: any) => {
    // Save user data and token
    setUser(response.user);
    localStorage.setItem('user', JSON.stringify(response.user));
    localStorage.setItem('token', response.token);

    console.log('Login successful:', response.user);

    // Log additional user information for debugging
    console.log('User role:', response.user.role);
    console.log('User permissions:', response.user.permissions);

    // Set last login time
    const lastLogin = new Date().toISOString();
    const updatedUser = { ...response.user, lastLogin };
    setUser(updatedUser);
    localStorage.setItem('user', JSON.stringify(updatedUser));

    // For hardcoded tokens (demo, admin, May Rakgama), skip JWT validation
    if (response.token.startsWith('demo-token-') ||
        response.token.startsWith('admin-token-') ||
        response.token.startsWith('may-token-')) {
      console.log('Hardcoded token detected, skipping JWT validation');
      return;
    }

    // Verify token is valid by parsing it (only for real JWT tokens)
    try {
      const tokenData = JSON.parse(atob(response.token.split('.')[1]));
      const expirationTime = tokenData.exp * 1000; // Convert to milliseconds
      const currentTime = Date.now();

      console.log('Token valid until:', new Date(expirationTime).toLocaleString());

      if (expirationTime < currentTime) {
        console.error('Received expired token from server');
        setError('Authentication error. Please try again.');
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        setUser(null);
        throw new Error('Received expired token');
      }
    } catch (tokenError) {
      console.error('Error parsing token:', tokenError);
      // For production, we'll be more lenient with token parsing errors
      // since we have hardcoded credentials
      console.log('Token parsing failed, but continuing with login for production compatibility');
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      console.log('Logging out user');

      // Call the auth API logout method
      try {
        await authAPI.logout();
        console.log('API logout successful');
      } catch (error) {
        console.error('API logout failed:', error);
        // Continue with local logout even if API call fails
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage and state regardless of API success
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('token');

      // Clear any other auth-related items from localStorage
      localStorage.removeItem('permissions');

      console.log('User logged out, storage cleared');
      setIsLoading(false);
    }
  };

  // Helper function to check if user has a specific permission
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;

    // Log permission check for debugging
    console.log(`Checking permission: ${permission} for user role: ${user.role}`);

    // Admin and May Rakgama have all permissions
    if (user.role === 'admin' || user.username === 'May Rakgama') {
      console.log('User is admin or May Rakgama, granting all permissions');
      return true;
    }

    // Check for 'all' permission
    if (user.permissions && user.permissions.includes('all')) {
      console.log('User has "all" permission');
      return true;
    }

    // Beta user permissions (limited access)
    if (user.role === 'beta') {
      const betaPermissions = [
        'view_animals', 'create_animal', 'update_animal', 'delete_animal',
        'view_health_records', 'create_health_record', 'update_health_record',
        'view_feeding_records', 'create_feeding_record', 'update_feeding_record',
        'view_basic_reports', 'export_basic_reports',
        'view_resources', 'view_government_data'
      ];

      if (betaPermissions.includes(permission)) {
        console.log(`Beta user has permission: ${permission}`);
        return true;
      }

      // Beta users cannot access premium features
      const premiumFeatures = [
        'view_breeding_records', 'create_breeding_record', 'update_breeding_record',
        'view_financial_records', 'create_financial_record', 'update_financial_record',
        'view_inventory', 'create_inventory', 'update_inventory',
        'view_commercial', 'create_commercial', 'update_commercial',
        'view_compliance', 'create_compliance', 'update_compliance',
        'view_advanced_analytics', 'view_ai_insights', 'view_predictive_analytics'
      ];

      if (premiumFeatures.includes(permission)) {
        console.log(`Beta user denied premium permission: ${permission}`);
        return false;
      }
    }

    // Enterprise/Professional user permissions (full access)
    if (user.role === 'enterprise' || user.role === 'professional') {
      console.log('Enterprise/Professional user, granting all permissions');
      return true;
    }

    // Manager role permissions
    if (user.role === 'manager' && [
      'view_animals', 'create_animal', 'update_animal',
      'view_health_records', 'create_health_record', 'update_health_record',
      'view_breeding_records', 'create_breeding_record', 'update_breeding_record',
      'view_transactions', 'create_transaction',
      'view_inventory', 'create_inventory', 'update_inventory',
      'view_reports', 'create_reports', 'export_reports'
    ].includes(permission)) {
      return true;
    }

    // Super user permissions
    if (user.role === 'super_user' && [
      'view_animals', 'create_animal', 'update_animal',
      'view_health_records', 'create_health_record',
      'view_breeding_records', 'create_breeding_record',
      'view_transactions',
      'view_inventory',
      'view_reports', 'export_reports'
    ].includes(permission)) {
      return true;
    }

    // Beta user permissions
    if (user.role === 'beta') {
      const betaPermissions = [
        'view_animals', 'create_animal', 'update_animal',
        'view_health_records', 'create_health_record', 'update_health_record',
        'view_feeding_records', 'create_feeding_record',
        'view_basic_reports', 'view_resources'
      ];

      if (betaPermissions.includes(permission)) {
        console.log(`Beta user has permission: ${permission}`);
        return true;
      }
    }

    // Check specific permission
    const hasSpecificPermission = user.permissions && user.permissions.includes(permission);
    console.log(`User ${hasSpecificPermission ? 'has' : 'does not have'} specific permission: ${permission}`);
    return hasSpecificPermission;
  };

  // Helper function to check if user is beta user
  const isBetaUser = (): boolean => {
    return user?.role === 'beta' || user?.username === 'Demo';
  };

  // Helper function to check if user is enterprise user
  const isEnterpriseUser = (): boolean => {
    return user?.role === 'enterprise' || user?.role === 'professional';
  };

  // Helper function to check if user is admin
  const isAdminUser = (): boolean => {
    return user?.role === 'admin' || user?.username === 'May Rakgama';
  };

  // Helper function to check if user can access a specific module
  const canAccessModule = (module: string): boolean => {
    if (!user) return false;

    // Admin and May Rakgama can access everything
    if (isAdminUser()) return true;

    // Enterprise users can access everything
    if (isEnterpriseUser()) return true;

    // Beta users have limited access
    if (isBetaUser()) {
      const betaModules = [
        'dashboard', 'animals', 'health', 'feeding', 'reports', 'resources', 'settings'
      ];

      const premiumModules = [
        'breeding', 'financial', 'inventory', 'commercial', 'compliance', 'analytics'
      ];

      if (betaModules.includes(module)) {
        return true;
      }

      if (premiumModules.includes(module)) {
        console.log(`Beta user denied access to premium module: ${module}`);
        return false;
      }
    }

    return false;
  };

  // Helper function to get user's access level
  const getAccessLevel = (): 'beta' | 'professional' | 'enterprise' | 'admin' => {
    if (!user) return 'beta';

    if (isAdminUser()) return 'admin';
    if (user.role === 'enterprise') return 'enterprise';
    if (user.role === 'professional') return 'professional';
    return 'beta';
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      isLoading,
      error,
      hasPermission,
      isBetaUser,
      isEnterpriseUser,
      isAdminUser,
      canAccessModule,
      getAccessLevel
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
