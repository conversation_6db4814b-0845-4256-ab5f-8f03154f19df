const express = require('express');
const router = express.Router();
const controller = require('../controllers/mongoDbController');

// Define routes
router.get('/', (req, res) => {
  res.status(200).json({ message: 'Inventory records endpoint' });
});

// Inventory routes
router.get('/items', controller.getInventory);
router.get('/items/:id', controller.getInventoryById);

// CRUD operations
router.post('/items', controller.createInventoryItem);
router.put('/items/:id', controller.updateInventoryItem);
router.delete('/items/:id', controller.deleteInventoryItem);

module.exports = router;