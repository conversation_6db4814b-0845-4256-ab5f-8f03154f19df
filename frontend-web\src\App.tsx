import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

// Core providers and context
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { SubscriptionProvider } from './contexts/SubscriptionContext';
import { SnackbarProvider } from './contexts/SnackbarContext';
import { MongoDbProvider } from './contexts/SimpleMongoDbContext';
import { DataProvider } from './contexts/DataContext';
import { LanguageProvider } from './contexts/LanguageContext';

// Core components
import { LazyLoadFallback } from './components/common';
import ErrorBoundary from './components/error/ComprehensiveErrorBoundary';
import UnifiedDashboardLayout from './layouts/UnifiedDashboardLayout';

// Utilities and hooks
import { lazyWithRetry } from './utils/lazyLoadUtils';

// Lazy load components with enhanced error handling and retry logic
// Main entry point components
const LandingPage = lazyWithRetry(() => import('./pages/FinalLandingPage'));
const EnhancedBeta = lazyWithRetry(() => import('./pages/EnhancedBeta'));
const BetaOnlyDashboard = lazyWithRetry(() => import('./pages/BetaOnlyDashboard'));
const LiveOnlyDashboard = lazyWithRetry(() => import('./pages/LiveOnlyDashboard'));
const EnhancedRegister = lazyWithRetry(() => import('./pages/EnhancedRegister'));

// Authentication components
const Login = lazyWithRetry(() => import('./pages/Login'));
const BetaLogin = lazyWithRetry(() => import('./pages/BetaLogin'));
const Register = lazyWithRetry(() => import('./pages/Register'));

// Dashboard components
const Dashboard = lazyWithRetry(() => import('./pages/dashboard/Dashboard'));

// Module components - All 12 main modules
const Animals = lazyWithRetry(() => import('./pages/animals/Animals'));
const Health = lazyWithRetry(() => import('./pages/Health'));
const Breeding = lazyWithRetry(() => import('./pages/breeding/Breeding'));
const Feeding = lazyWithRetry(() => import('./pages/feeding/Feeding'));
const Financial = lazyWithRetry(() => import('./pages/Financial'));
const Inventory = lazyWithRetry(() => import('./pages/inventory/InventoryDashboard'));
const Commercial = lazyWithRetry(() => import('./pages/commercial/Commercial'));
const Reports = lazyWithRetry(() => import('./pages/Reports'));
const Resources = lazyWithRetry(() => import('./pages/Resources'));
const Settings = lazyWithRetry(() => import('./pages/Settings'));
const Compliance = lazyWithRetry(() => import('./pages/Compliance'));
const Analytics = lazyWithRetry(() => import('./pages/Analytics'));

// Business Analysis
const BusinessAnalysis = lazyWithRetry(() => import('./pages/BusinessAnalysis'));

function App() {
  return (
    <ErrorBoundary>
      <LanguageProvider>
        <ThemeProvider>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <SnackbarProvider>
              <MongoDbProvider>
                <DataProvider>
                  <AuthProvider>
                    <SubscriptionProvider>
                      <Router>
                        <Suspense fallback={<LazyLoadFallback />}>
                          <Routes>
                            {/* Landing and Authentication Routes */}
                            <Route path="/" element={<LandingPage />} />
                            <Route path="/login" element={<Login />} />
                            <Route path="/beta-login" element={<BetaLogin />} />
                            <Route path="/register" element={<Register />} />
                            <Route path="/enhanced-beta" element={<EnhancedBeta />} />
                            <Route path="/enhanced-register" element={<EnhancedRegister />} />

                            {/* Dashboard Routes - Beta users see limited modules */}
                            <Route path="/dashboard/*" element={<EnhancedBeta />} />

                            {/* Live Dashboard Routes - Full access for live users */}
                            <Route path="/live-dashboard/*" element={<LiveOnlyDashboard />} />
                          </Routes>
                        </Suspense>
                      </Router>
                    </SubscriptionProvider>
                  </AuthProvider>
                </DataProvider>
              </MongoDbProvider>
            </SnackbarProvider>
          </LocalizationProvider>
        </ThemeProvider>
      </LanguageProvider>
    </ErrorBoundary>
  );
}

export default App;