import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import {
  LazyLoadFallback,
  ErrorBoundary
} from './components/common';
import ComprehensiveErrorBoundary from './components/error/ComprehensiveErrorBoundary';
import { AuthProvider } from './contexts/AuthContext';
import { SnackbarProvider } from './contexts/SnackbarContext';
import { DataProvider } from './contexts/DataContext';
import { LanguageProvider, useLanguage } from './contexts/LanguageContext';
import { MongoDbProvider } from './contexts/SimpleMongoDbContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { ModernThemeProvider } from './contexts/ModernThemeContext';
import { SubscriptionProvider } from './contexts/SubscriptionContext';
import UnifiedDashboardLayout from './layouts/UnifiedDashboardLayout';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { lazyWithRetry, preloadComponents } from './utils/lazyLoadUtils';
import LanguageTest from './components/LanguageTest';
// Import dayjs locales for supported languages
import 'dayjs/locale/en-gb'; // English (UK)
import 'dayjs/locale/af'; // Afrikaans
// Note: Some South African languages don't have specific dayjs locales
// We'll use en-gb as fallback for those
import './utils/dateUtils'; // Import date utilities to register plugins
import './styles/tabFixes.css'; // Import tab fixes for proper navigation
import './styles/agriIntelTheme.css'; // Import AgriIntel theme system
import { useNavigationFixes } from './utils/navigationFixes'; // Import navigation fixes
import { AgriIntelThemeProvider } from './components/theme/AgriIntelThemeProvider'; // Import theme provider

// Main entry point components
const LandingPage = lazyWithRetry(() => import('./pages/BasicLanding'), 'basic-landing');
const EnhancedBeta = lazyWithRetry(() => import('./pages/EnhancedBeta'), 'enhanced-beta');
const BetaOnlyDashboard = lazyWithRetry(() => import('./pages/BetaOnlyDashboard'), 'beta-only-dashboard');
const LiveOnlyDashboard = lazyWithRetry(() => import('./pages/LiveOnlyDashboard'), 'live-only-dashboard');
const EnhancedRegister = lazyWithRetry(() => import('./pages/EnhancedRegister'), 'enhanced-register');

// Authentication components
const Login = lazyWithRetry(() => import('./pages/Login'), 'login');
const BetaLogin = lazyWithRetry(() => import('./pages/BetaLogin'), 'beta-login');
const Register = lazyWithRetry(() => import('./pages/Register'), 'register');

// Lazy load page components by feature groups with enhanced error handling
const Dashboard = lazyWithRetry(() => import('./pages/dashboard/Dashboard'), 'dashboard');
const Animals = lazyWithRetry(() => import('./pages/animals/Animals'), 'animals');
const AnimalsDashboard = lazyWithRetry(() => import('./pages/animals/AnimalsDashboard'), 'animals-dashboard');
const Breeding = lazyWithRetry(() => import('./pages/breeding/Breeding'), 'breeding');
const BreedingDashboard = lazyWithRetry(() => import('./pages/breeding/BreedingDashboard'), 'breeding-dashboard');
const Health = lazyWithRetry(() => import('./pages/health/Health'), 'health');
const HealthDashboard = lazyWithRetry(() => import('./pages/health/HealthDashboard'), 'health-dashboard');
const Feeding = lazyWithRetry(() => import('./pages/feeding/Feeding'), 'feeding');
const FeedingDashboard = lazyWithRetry(() => import('./pages/feeding/FeedingDashboard'), 'feeding-dashboard');
const Financial = lazyWithRetry(() => import('./pages/financial/Financial'), 'financial');
const FinancialDashboard = lazyWithRetry(() => import('./pages/financial/FinancialDashboard'), 'financial-dashboard');
const InventoryDashboard = lazyWithRetry(() => import('./pages/inventory/InventoryDashboard'), 'inventory-dashboard');
const Resources = lazyWithRetry(() => import('./pages/resources/Resources'), 'resources');
const ResourcesDashboard = lazyWithRetry(() => import('./pages/resources/ResourcesDashboard'), 'resources-dashboard');
const Commercial = lazyWithRetry(() => import('./pages/commercial/Commercial'), 'commercial');
const CommercialDashboard = lazyWithRetry(() => import('./pages/commercial/CommercialDashboard'), 'commercial-dashboard');
const ReportsDashboard = lazyWithRetry(() => import('./pages/reports/ReportsDashboard'), 'reports-dashboard');
const AnalysisReport = lazyWithRetry(() => import('./pages/reports/AnalysisReport'), 'analysis-report');
const PerformanceReport = lazyWithRetry(() => import('./pages/reports/PerformanceReport'), 'performance-report');
const HealthReport = lazyWithRetry(() => import('./pages/reports/HealthReport'), 'health-report');
const MarketReport = lazyWithRetry(() => import('./pages/reports/MarketReport'), 'market-report');
const FinancialReport = lazyWithRetry(() => import('./pages/reports/FinancialReport'), 'financial-report');
const FeedingReports = lazyWithRetry(() => import('./pages/reports/FeedingReports'), 'feeding-reports');
const PredictiveAnalysis = lazyWithRetry(() => import('./pages/reports/PredictiveAnalysis'), 'predictive-analysis');
const ComplianceDashboard = lazyWithRetry(() => import('./pages/compliance/ComplianceDashboard'), 'compliance-dashboard');
const Compliance = lazyWithRetry(() => import('./pages/Compliance'), 'compliance');
const Settings = lazyWithRetry(() => import('./pages/Settings'), 'settings');
const DatabaseSettings = lazyWithRetry(() => import('./pages/settings/DatabaseSettings'), 'database-settings');
const ThemeSettings = lazyWithRetry(() => import('./pages/settings/ThemeSettings'), 'theme-settings');
const BackupSettings = lazyWithRetry(() => import('./pages/settings/BackupSettings'), 'backup-settings');
const UserManagement = lazyWithRetry(() => import('./pages/settings/UserManagement'), 'user-management');
const TranslationTest = lazyWithRetry(() => import('./pages/TranslationTest'), 'translation-test');
const AccessControlDemo = lazyWithRetry(() => import('./pages/AccessControlDemo'), 'access-control-demo');

const BusinessAnalysisDashboard = lazyWithRetry(() => import('./pages/analytics/BusinessAnalysisDashboard'), 'business-analysis-dashboard');
const BusinessStrategy = lazyWithRetry(() => import('./pages/analytics/BusinessStrategy'), 'business-strategy');
const BusinessPredictions = lazyWithRetry(() => import('./pages/analytics/BusinessPredictions'), 'business-predictions');
const AssetManagement = lazyWithRetry(() => import('./pages/animals/AssetManagement'), 'asset-management');
const ErrorPage = lazyWithRetry(() => import('./pages/ErrorPage'), 'error-page');

// BETA Upgrade Components
const BreedingUpgrade = lazyWithRetry(() => import('./components/beta/BreedingUpgrade'), 'breeding-upgrade');
const FinancialUpgrade = lazyWithRetry(() => import('./components/beta/FinancialUpgrade'), 'financial-upgrade');
const InventoryUpgrade = lazyWithRetry(() => import('./components/beta/InventoryUpgrade'), 'inventory-upgrade');
const CommercialUpgrade = lazyWithRetry(() => import('./components/beta/CommercialUpgrade'), 'commercial-upgrade');
const AnalyticsUpgrade = lazyWithRetry(() => import('./components/beta/AnalyticsUpgrade'), 'analytics-upgrade');
const ComplianceUpgrade = lazyWithRetry(() => import('./components/beta/ComplianceUpgrade'), 'compliance-upgrade');

// Testing Components
const TestingDashboard = lazyWithRetry(() => import('./pages/testing/TestingDashboard'), 'testing-dashboard');

/**
 * Component to apply navigation fixes inside Router context
 */
const NavigationFixesProvider: React.FC = () => {
  // Apply navigation fixes (must be inside Router context)
  useNavigationFixes();
  return null; // This component doesn't render anything
};

/**
 * Preload critical components for better user experience with priorities
 * This improves initial load time and responsiveness
 */
preloadComponents([
  // Critical components (highest priority - load first)
  [() => import('./pages/BasicLanding'), 'basic-landing', 10],
  [() => import('./pages/Login'), 'login', 10],
  [() => import('./pages/dashboard/Dashboard'), 'dashboard', 10],

  // High priority components (essential modules)
  [() => import('./pages/animals/AnimalsDashboard'), 'animals-dashboard', 8],
  [() => import('./pages/health/HealthDashboard'), 'health-dashboard', 8],
  [() => import('./pages/financial/FinancialDashboard'), 'financial-dashboard', 8],

  // Medium priority components (important modules)
  [() => import('./pages/breeding/BreedingDashboard'), 'breeding-dashboard', 5],
  [() => import('./pages/feeding/FeedingDashboard'), 'feeding-dashboard', 5],
  [() => import('./pages/analytics/BusinessAnalysisDashboard'), 'business-analysis-dashboard', 5],

  // Lower priority components (secondary modules)
  [() => import('./pages/reports/ReportsDashboard'), 'reports-dashboard', 3],
  [() => import('./pages/resources/ResourcesDashboard'), 'resources-dashboard', 3],

  // Lowest priority components (rarely used modules)
  [() => import('./pages/settings/ThemeSettings'), 'theme-settings', 1],
  [() => import('./pages/settings/UserManagement'), 'user-management', 1]
]);

/**
 * Main App component that sets up the application structure
 * Uses our custom ThemeProvider that includes all theme functionality
 */
function App() {
  return (
    <ErrorBoundary>
      <AgriIntelThemeProvider>
        <ThemeProvider>
          <AuthProvider>
            <SubscriptionProvider>
              <SnackbarProvider>
                <MongoDbProvider>
                  <DataProvider>
                    <LanguageProvider>
                      <AppWithLocalization />
                    </LanguageProvider>
                  </DataProvider>
                </MongoDbProvider>
              </SnackbarProvider>
            </SubscriptionProvider>
          </AuthProvider>
        </ThemeProvider>
      </AgriIntelThemeProvider>
    </ErrorBoundary>
  );
}

/**
 * Component to handle localization with the selected language
 * Provides routing and authentication protection for the application
 */
const AppWithLocalization: React.FC = () => {
  const { language, translate, isLoading } = useLanguage();

  // Map language codes to localization adapter locales
  const getLocale = (lang: string): string => {
    const localeMap: Record<string, string> = {
      'en': 'en-gb',
      'af': 'af',
      'zu': 'en-gb', // Fallback for Zulu
      'xh': 'en-gb', // Fallback for Xhosa
      'st': 'en-gb', // Fallback for Sotho
      'tn': 'en-gb', // Fallback for Tswana
      've': 'en-gb', // Fallback for Venda
      'ts': 'en-gb', // Fallback for Tsonga
      'nr': 'en-gb', // Fallback for Ndebele
      'ss': 'en-gb'  // Fallback for Swati
    };

    return localeMap[lang] || 'en-gb';
  };

  // Show loading indicator if translations are still loading
  if (isLoading) {
    return <LazyLoadFallback message={translate('common.loading')} />;
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={getLocale(language)}>
      <Router>
        <NavigationFixesProvider />
        <Suspense fallback={<LazyLoadFallback message={translate('common.loading')} />}>
          <Routes>
            {/* Landing and Authentication Routes */}
            <Route path="/" element={<LandingPage />} />
            <Route path="/beta" element={<EnhancedBeta />} />
            <Route path="/beta-login" element={<BetaLogin />} />
            {/* BETA Dashboard - Redirect to main dashboard with sidebar */}
            <Route path="/dashboard" element={<LiveOnlyDashboard />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<EnhancedRegister />} />

            {/* BETA Dashboard Routes - Direct Access */}
            <Route
              path="/beta-dashboard/*"
              element={<UnifiedDashboardLayout />}
            >
              {/* BETA-only routes with limited access */}
              <Route index element={<Dashboard />} />
              <Route path="animals" element={<AnimalsDashboard />} />
              <Route path="animals/*" element={<Animals />} />
              <Route path="health" element={<HealthDashboard />} />
              <Route path="health/*" element={<Health />} />
              <Route path="resources" element={<ResourcesDashboard />} />
              <Route path="resources/*" element={<Resources />} />
              <Route path="settings/*" element={<Settings />} />

              {/* Locked premium routes - show upgrade prompts */}
              <Route path="breeding" element={<BreedingUpgrade />} />
              <Route path="breeding/*" element={<BreedingUpgrade />} />
              <Route path="financial" element={<FinancialUpgrade />} />
              <Route path="financial/*" element={<FinancialUpgrade />} />
              <Route path="inventory" element={<InventoryUpgrade />} />
              <Route path="commercial" element={<CommercialUpgrade />} />
              <Route path="commercial/*" element={<CommercialUpgrade />} />
              <Route path="analytics" element={<AnalyticsUpgrade />} />
              <Route path="analytics/*" element={<AnalyticsUpgrade />} />
              <Route path="compliance" element={<ComplianceUpgrade />} />
              <Route path="compliance/*" element={<ComplianceUpgrade />} />
            </Route>

            {/* Live Dashboard Routes - Direct Access */}
            <Route
              path="/dashboard"
              element={<UnifiedDashboardLayout />}
            >
            {/* Main Dashboard */}
            <Route index element={<Dashboard />} />

            {/* Animal Management Module */}
            <Route path="animals" element={<AnimalsDashboard />} />
            <Route path="animals/*" element={<Animals />} />
            <Route path="asset-management/*" element={<AssetManagement />} />

            {/* Breeding Management Module */}
            <Route path="breeding" element={<BreedingDashboard />} />
            <Route path="breeding/*" element={<Breeding />} />

            {/* Health Management Module */}
            <Route path="health" element={<HealthDashboard />} />
            <Route path="health/*" element={<Health />} />

            {/* Feeding Management Module */}
            <Route path="feeding" element={<FeedingDashboard />} />
            <Route path="feeding/*" element={<Feeding />} />

            {/* Financial Management Module */}
            <Route path="financial" element={<FinancialDashboard />} />
            <Route path="financial/*" element={<Financial />} />

            {/* Inventory Management Module */}
            <Route path="inventory" element={<InventoryDashboard />} />

            {/* Resources Management Module */}
            <Route path="resources" element={<ResourcesDashboard />} />
            <Route path="resources/*" element={<Resources />} />

            {/* Commercial Module */}
            <Route path="commercial" element={<CommercialDashboard />} />
            <Route path="commercial/*" element={<Commercial />} />

            {/* Reports Module */}
            <Route path="reports" element={<ReportsDashboard />} />
            <Route path="reports/dashboard" element={<ReportsDashboard />} />
            <Route path="reports/analysis" element={<AnalysisReport />} />
            <Route path="reports/performance" element={<PerformanceReport />} />
            <Route path="reports/health" element={<HealthReport />} />
            <Route path="reports/market" element={<MarketReport />} />
            <Route path="reports/financial" element={<FinancialReport />} />
            <Route path="reports/feeding" element={<FeedingReports />} />
            <Route path="reports/predictive" element={<PredictiveAnalysis />} />

            {/* Business Analytics Module */}
            <Route path="analytics" element={<BusinessAnalysisDashboard />} />
            <Route path="analytics/strategy" element={<BusinessStrategy />} />
            <Route path="analytics/predictions" element={<BusinessPredictions />} />

            {/* Compliance Module */}
            <Route path="compliance" element={<ComplianceDashboard />} />
            <Route path="compliance/*" element={<Compliance />} />

            {/* Settings Module */}
            <Route path="settings/*" element={<Settings />} />
            <Route path="settings/database" element={<DatabaseSettings />} />
            <Route path="settings/theme" element={<ThemeSettings />} />
            <Route path="settings/backup" element={<BackupSettings />} />
            <Route path="settings/users" element={<UserManagement />} />

            {/* Development & Testing */}
            <Route path="language-test" element={<LanguageTest />} />
            <Route path="translation-test" element={<TranslationTest />} />
            <Route path="access-control-demo" element={<AccessControlDemo />} />
            <Route path="testing" element={<TestingDashboard />} />

            {/* Error Page - Catch All */}
            <Route path="*" element={<ErrorPage />} />
          </Route>
          </Routes>
        </Suspense>
      </Router>
    </LocalizationProvider>
  );
};

export default App;
