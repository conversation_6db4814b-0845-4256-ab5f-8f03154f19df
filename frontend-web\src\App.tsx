import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import {
  LazyLoadFallback,
  ErrorBoundary
} from './components/common';
import ComprehensiveErrorBoundary from './components/error/ComprehensiveErrorBoundary';
import { AuthProvider } from './contexts/AuthContext';
import { SnackbarProvider } from './contexts/SnackbarContext';
import { DataProvider } from './contexts/DataContext';
import { LanguageProvider, useLanguage } from './contexts/LanguageContext';
import { MongoDbProvider } from './contexts/SimpleMongoDbContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { ModernThemeProvider } from './contexts/ModernThemeContext';
import { SubscriptionProvider } from './contexts/SubscriptionContext';
import UnifiedDashboardLayout from './layouts/UnifiedDashboardLayout';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { lazyWithRetry, preloadComponents } from './utils/lazyLoadUtils';
import LanguageTest from './components/LanguageTest';
import { UnifiedThemeProvider } from './contexts/UnifiedThemeContext';

// Import your original working landing page
const LandingPage = React.lazy(() => import('./pages/OriginalLandingPage'));

// Authentication components
const Login = React.lazy(() => import('./pages/Login'));
const BetaLogin = React.lazy(() => import('./pages/BetaLogin'));

// Dashboard components
const Dashboard = React.lazy(() => import('./pages/dashboard/Dashboard'));
const BetaOnlyDashboard = React.lazy(() => import('./pages/BetaOnlyDashboard'));

function App() {
  return (
    <ComprehensiveErrorBoundary>
      <CssBaseline />
      <LanguageProvider>
        <UnifiedThemeProvider>
          <ModernThemeProvider>
            <ThemeProvider>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <SnackbarProvider>
                  <MongoDbProvider>
                    <DataProvider>
                      <AuthProvider>
                        <SubscriptionProvider>
                          <Router>
                            <Suspense fallback={<LazyLoadFallback />}>
                              <Routes>
                                <Route path="/" element={<LandingPage />} />
                                <Route path="/login" element={<Login />} />
                                <Route path="/beta-login" element={<BetaLogin />} />
                                <Route path="/dashboard/*" element={<Dashboard />} />
                                <Route path="/beta-dashboard/*" element={<BetaOnlyDashboard />} />
                                <Route path="*" element={<Navigate to="/" replace />} />
                              </Routes>
                            </Suspense>
                          </Router>
                        </SubscriptionProvider>
                      </AuthProvider>
                    </DataProvider>
                  </MongoDbProvider>
                </SnackbarProvider>
              </LocalizationProvider>
            </ThemeProvider>
          </ModernThemeProvider>
        </UnifiedThemeProvider>
      </LanguageProvider>
    </ComprehensiveErrorBoundary>
  );
}

export default App;