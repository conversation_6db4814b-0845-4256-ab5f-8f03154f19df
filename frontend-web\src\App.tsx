import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

// Core providers and context
import { AgriIntelThemeProvider } from './contexts/AgriIntelThemeContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { SubscriptionProvider } from './contexts/SubscriptionContext';
import { SnackbarProvider } from './contexts/SnackbarContext';
import { MongoDbProvider } from './contexts/MongoDbContext';
import { DataProvider } from './contexts/DataContext';
import { LanguageProvider, useLanguage } from './contexts/LanguageContext';

// Core components
import ErrorBoundary from './components/ErrorBoundary';
import LazyLoadFallback from './components/LazyLoadFallback';
import UnifiedDashboardLayout from './components/layout/UnifiedDashboardLayout';
import LanguageTest from './components/LanguageTest';

// Utilities and hooks
import { lazyWithRetry, preloadComponents } from './utils/lazyLoading';
import { useNavigationFixes } from './hooks/useNavigationFixes';

// Lazy load components with enhanced error handling and retry logic
// This improves performance by only loading components when needed

// Main entry point components
const LandingPage = lazyWithRetry(() => import('./pages/BasicLanding'), 'basic-landing');
const EnhancedBeta = lazyWithRetry(() => import('./pages/EnhancedBeta'), 'enhanced-beta');
const BetaOnlyDashboard = lazyWithRetry(() => import('./pages/BetaOnlyDashboard'), 'beta-only-dashboard');
const LiveOnlyDashboard = lazyWithRetry(() => import('./pages/LiveOnlyDashboard'), 'live-only-dashboard');
const EnhancedRegister = lazyWithRetry(() => import('./pages/EnhancedRegister'), 'enhanced-register');

// Authentication components
const Login = lazyWithRetry(() => import('./pages/SimpleLogin'), 'simple-login');
const BetaLogin = lazyWithRetry(() => import('./pages/SimpleBetaLogin'), 'simple-beta-login');
const Register = lazyWithRetry(() => import('./pages/Register'), 'register');