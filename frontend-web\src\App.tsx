import React from 'react';

function App() {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #2E7D32, #4CAF50)',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      padding: '2rem'
    }}>
      <header style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '3rem',
        padding: '1rem',
        background: 'rgba(0,0,0,0.2)',
        borderRadius: '10px'
      }}>
        <h1 style={{ margin: 0, fontSize: '2rem' }}>AgriIntel</h1>
        <div>
          <button style={{
            padding: '0.8rem 1.5rem',
            margin: '0 0.5rem',
            background: '#FF9800',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: 'bold'
          }}>
            BETA ACCESS
          </button>
          <button style={{
            padding: '0.8rem 1.5rem',
            margin: '0 0.5rem',
            background: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: 'bold'
          }}>
            LIVE ACCESS
          </button>
        </div>
      </header>

      <main style={{ textAlign: 'center', maxWidth: '1200px', margin: '0 auto' }}>
        <h2 style={{ fontSize: '3rem', marginBottom: '1rem' }}>
          Smart Livestock Management for South Africa
        </h2>

        <p style={{ fontSize: '1.5rem', marginBottom: '3rem', opacity: 0.9 }}>
          Join 15,000+ farmers who have transformed their operations with AI technology
        </p>

        <div style={{ marginBottom: '4rem' }}>
          <button style={{
            padding: '1.5rem 3rem',
            margin: '1rem',
            background: '#FF9800',
            color: 'white',
            border: 'none',
            borderRadius: '10px',
            cursor: 'pointer',
            fontSize: '1.3rem',
            fontWeight: 'bold'
          }}>
            START FREE BETA
          </button>
          <button style={{
            padding: '1.5rem 3rem',
            margin: '1rem',
            background: 'transparent',
            color: 'white',
            border: '2px solid white',
            borderRadius: '10px',
            cursor: 'pointer',
            fontSize: '1.3rem',
            fontWeight: 'bold'
          }}>
            GO LIVE
          </button>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '2rem',
          marginBottom: '4rem'
        }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '2rem',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🐄</div>
            <h3>Smart Livestock Tracking</h3>
            <p>Real-time monitoring of cattle, sheep, and goats</p>
          </div>

          <div style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '2rem',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🩺</div>
            <h3>AI Health Management</h3>
            <p>Predictive health analytics to prevent diseases</p>
          </div>

          <div style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '2rem',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
            <h3>Advanced Analytics</h3>
            <p>Comprehensive insights and reporting</p>
          </div>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '2rem',
          marginBottom: '4rem'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#FFD700' }}>15K+</div>
            <div>South African Farmers</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#FFD700' }}>750K+</div>
            <div>Livestock Tracked</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#FFD700' }}>99.9%</div>
            <div>System Uptime</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#FFD700' }}>40%</div>
            <div>Cost Reduction</div>
          </div>
        </div>
      </main>

      <footer style={{
        textAlign: 'center',
        padding: '2rem',
        background: 'rgba(0,0,0,0.2)',
        borderRadius: '10px',
        marginTop: '3rem'
      }}>
        <h3>AgriIntel</h3>
        <p>Smart Livestock Management for South African Farmers</p>
        <p style={{ marginTop: '1rem', opacity: 0.7 }}>
          © 2024 AgriIntel. All rights reserved.
        </p>
      </footer>
    </div>
  );
}

export default App;