import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  But<PERSON>,
  Grid,
  Card,
  CardContent,
  AppBar,
  Toolbar,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Chip,
  Alert,
  LinearProgress,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  useTheme,
  alpha
} from '@mui/material';
import {
  Agriculture,
  Dashboard,
  Pets,
  LocalHospital,
  TrendingUp,
  AccountBalance,
  Inventory,
  Assessment,
  Settings,
  Lock,
  Star,
  Upgrade,
  Timer,
  Person,
  ExitToApp,
  Language,
  Palette,
  NotificationsActive,
  Security,
  Visibility
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import LanguageSelector from '../components/LanguageSelector';
import { useTranslation } from '../hooks/useTranslation';
import ModernConnectedCard from '../components/common/ModernConnectedCard';
import ImageShowcase from '../components/showcase/ImageShowcase';
import { hasModuleAccess, getUpgradeMessage, moduleAccessConfig } from '../utils/betaAccessControl';
import { useAuth } from '../contexts/AuthContext';
import TabbedBetaInterface from '../components/beta/TabbedBetaInterface';

// Modern unified design system (same as landing page)
const lifeNatureDesign = {
  colors: {
    // Primary Green Life Palette
    primary: '#2E7D32', // Deep Forest Green
    primaryLight: '#4CAF50', // Life Green
    primaryDark: '#1B5E20', // Dark Forest

    // Secondary Nature Palette
    secondary: '#388E3C', // Nature Green
    secondaryLight: '#66BB6A', // Fresh Green
    secondaryDark: '#2E7D32', // Deep Nature

    // Accent Colors
    accent: '#81C784', // Soft Life Green
    accentBright: '#A5D6A7', // Bright Nature
    accentGold: '#FFC107', // Golden Harvest

    // Surface & Background
    surface: 'rgba(76, 175, 80, 0.15)', // Translucent Green
    surfaceLight: 'rgba(129, 199, 132, 0.1)', // Light Green Surface
    background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 25%, #388E3C 50%, #4CAF50 75%, #66BB6A 100%)',

    // Text Colors
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)',
    textAccent: '#E8F5E8',

    // Gradients
    gradient: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 30%, #4CAF50 60%, #81C784 100%)',
    accentGradient: 'linear-gradient(135deg, #4CAF50 0%, #81C784 100%)',
    cardGradient: 'linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(129, 199, 132, 0.1) 100%)'
  },
  shapes: {
    borderRadius: {
      small: '12px',
      medium: '20px',
      large: '32px',
      xl: '48px'
    },
    clipPath: {
      hexagon: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
      diamond: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
      leaf: 'polygon(50% 0%, 80% 10%, 100% 35%, 85% 70%, 50% 100%, 15% 70%, 0% 35%, 20% 10%)',
      organic: '30% 70% 70% 30% / 30% 30% 70% 70%',
      circle: '50%',
      pentagon: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)'
    }
  },
  gradients: {
    primary: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
    secondary: 'linear-gradient(135deg, #388E3C 0%, #66BB6A 100%)',
    accent: 'linear-gradient(135deg, #81C784 0%, #A5D6A7 100%)',
    life: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 30%, #4CAF50 60%, #81C784 100%)',
    nature: 'linear-gradient(45deg, #2E7D32 0%, #388E3C 25%, #4CAF50 50%, #66BB6A 75%, #81C784 100%)',
    harvest: 'linear-gradient(135deg, #4CAF50 0%, #FFC107 50%, #FF8F00 100%)',
    pageBackground: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 25%, #388E3C 50%, #4CAF50 75%, #66BB6A 100%)'
  }
};

// Modern motion graphics background (same as landing page)
const MotionGraphicsBackground: React.FC = () => {
  const shapes = Array.from({ length: 12 }, (_, i) => ({
    id: i,
    shape: Object.values(lifeNatureDesign.shapes.clipPath)[i % 5],
    size: Math.random() * 80 + 40,
    x: Math.random() * 100,
    y: Math.random() * 100,
    duration: Math.random() * 25 + 20,
    delay: Math.random() * 5
  }));

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        overflow: 'hidden',
        pointerEvents: 'none',
        zIndex: 0
      }}
    >
      {/* Animated gradient background */}
      <motion.div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: lifeNatureDesign.colors.gradient,
        }}
        animate={{
          background: [
            lifeNatureDesign.colors.gradient,
            'linear-gradient(135deg, #1A1F3A 0%, #0A0E27 50%, #2D1B69 100%)',
            lifeNatureDesign.colors.gradient
          ]
        }}
        transition={{ duration: 12, repeat: Infinity, ease: "easeInOut" }}
      />

      {/* Floating geometric shapes */}
      {shapes.map((shape) => (
        <motion.div
          key={shape.id}
          style={{
            position: 'absolute',
            width: shape.size,
            height: shape.size,
            background: `linear-gradient(45deg, ${alpha(lifeNatureDesign.colors.accent, 0.08)}, ${alpha(lifeNatureDesign.colors.accentBright, 0.08)})`,
            clipPath: shape.shape,
            left: `${shape.x}%`,
            top: `${shape.y}%`,
          }}
          animate={{
            rotate: [0, 360],
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.5, 0.2],
            x: [0, Math.random() * 50 - 25, 0],
            y: [0, Math.random() * 50 - 25, 0],
          }}
          transition={{
            duration: shape.duration,
            repeat: Infinity,
            ease: "easeInOut",
            delay: shape.delay,
          }}
        />
      ))}
    </Box>
  );
};

// Modern shaped card component (same as landing page)
interface ModernCardProps {
  children: React.ReactNode;
  shape?: 'hexagon' | 'diamond' | 'arrow' | 'pentagon' | 'parallelogram' | 'rounded';
  variant?: 'glass' | 'solid' | 'gradient' | 'neon';
  size?: 'small' | 'medium' | 'large';
  animate?: boolean;
  sx?: any;
}

const ModernCard: React.FC<ModernCardProps> = ({
  children,
  shape = 'rounded',
  variant = 'glass',
  size = 'medium',
  animate = true,
  sx = {}
}) => {
  const getCardStyles = () => {
    const baseStyles = {
      position: 'relative',
      overflow: 'hidden',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    };

    const sizeStyles = {
      small: { minHeight: '180px' },
      medium: { minHeight: '250px' },
      large: { minHeight: '320px' }
    };

    const shapeStyles = {
      hexagon: {
        clipPath: lifeNatureDesign.shapes.clipPath.hexagon,
        borderRadius: 0
      },
      diamond: {
        clipPath: lifeNatureDesign.shapes.clipPath.diamond,
        borderRadius: 0,
        transform: 'rotate(0deg)'
      },
      arrow: {
        clipPath: lifeNatureDesign.shapes.clipPath.leaf,
        borderRadius: 0
      },
      pentagon: {
        clipPath: lifeNatureDesign.shapes.clipPath.pentagon,
        borderRadius: 0
      },
      parallelogram: {
        clipPath: lifeNatureDesign.shapes.clipPath.organic,
        borderRadius: 0
      },
      rounded: {
        borderRadius: lifeNatureDesign.shapes.borderRadius.large
      }
    };

    const variantStyles = {
      glass: {
        background: lifeNatureDesign.colors.surface,
        backdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.2)}`,
        boxShadow: `0 8px 32px ${alpha('#000', 0.3)}`
      },
      solid: {
        background: lifeNatureDesign.colors.secondary,
        border: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.3)}`,
        boxShadow: `0 8px 32px ${alpha('#000', 0.4)}`
      },
      gradient: {
        background: lifeNatureDesign.colors.cardGradient,
        border: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.3)}`,
        boxShadow: `0 8px 32px ${alpha(lifeNatureDesign.colors.accent, 0.2)}`
      },
      neon: {
        background: lifeNatureDesign.colors.surface,
        border: `2px solid ${lifeNatureDesign.colors.accent}`,
        boxShadow: `0 0 20px ${alpha(lifeNatureDesign.colors.accent, 0.5)}, inset 0 0 20px ${alpha(lifeNatureDesign.colors.accent, 0.1)}`
      }
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...shapeStyles[shape],
      ...variantStyles[variant],
      ...sx
    };
  };

  const cardContent = (
    <Box sx={getCardStyles()}>
      {/* Animated background pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `radial-gradient(circle at 30% 30%, ${alpha(lifeNatureDesign.colors.accent, 0.08)} 0%, transparent 50%)`,
          zIndex: 0
        }}
      />

      {/* Content */}
      <Box sx={{ position: 'relative', zIndex: 1, height: '100%', p: 3 }}>
        {children}
      </Box>
    </Box>
  );

  if (!animate) return cardContent;

  return (
    <motion.div
      whileHover={{
        scale: 1.02,
        y: -8,
        transition: { duration: 0.3 }
      }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
    >
      {cardContent}
    </motion.div>
  );
};

interface BetaModule {
  id: string;
  name: string;
  icon: React.ReactElement;
  description: string;
  isLocked: boolean;
  isPremium: boolean;
  comingSoon?: boolean;
  color: string;
  route?: string;
  backgroundImage?: string;
}

const EnhancedBeta: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate } = useTranslation();
  const { user: authUser } = useAuth();

  // Check authentication but don't auto-redirect - let user navigate manually
  React.useEffect(() => {
    if (!authUser) {
      console.log('No authenticated user - user should login via beta-login page');
    }
  }, [authUser]);

  const [user, setUser] = useState({
    name: authUser?.firstName || 'Beta User',
    email: authUser?.email || '<EMAIL>',
    trialDaysLeft: 27,
    animalsCount: 12,
    maxAnimals: 50
  });
  
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [profileAnchor, setProfileAnchor] = useState<null | HTMLElement>(null);
  const [selectedModule, setSelectedModule] = useState<BetaModule | null>(null);
  const [showImageShowcase, setShowImageShowcase] = useState(false);

  // Fix for white blur overlay issue
  React.useEffect(() => {
    // Remove any persistent overlays
    const removeOverlays = () => {
      const overlays = document.querySelectorAll('.MuiBackdrop-root:not(.MuiBackdrop-open)');
      overlays.forEach(overlay => {
        (overlay as HTMLElement).style.display = 'none';
        (overlay as HTMLElement).style.pointerEvents = 'none';
      });

      // Ensure body doesn't have blur
      document.body.style.filter = 'none';
      document.body.style.backdropFilter = 'none';

      // Remove any white overlays
      const whiteOverlays = document.querySelectorAll('[style*="rgba(255, 255, 255"]');
      whiteOverlays.forEach(overlay => {
        if ((overlay as HTMLElement).style.zIndex === '1300' ||
            (overlay as HTMLElement).style.position === 'fixed') {
          (overlay as HTMLElement).style.display = 'none';
        }
      });
    };

    removeOverlays();
    const interval = setInterval(removeOverlays, 1000);

    return () => clearInterval(interval);
  }, []);

  // Enhanced livestock statistics
  const livestockStats = {
    totalAnimals: 247,
    healthyAnimals: 231,
    pregnantAnimals: 18,
    newBorns: 12,
    avgWeight: 485,
    milkProduction: 1250,
    feedConsumption: 2840,
    vaccinations: 45,
    breeds: ['Holstein', 'Angus', 'Hereford', 'Simmental'],
    recentActivities: [
      { type: 'birth', animal: 'Cow #247', time: '2 hours ago', icon: '🐄' },
      { type: 'vaccination', animal: 'Bull #023', time: '4 hours ago', icon: '💉' },
      { type: 'health_check', animal: 'Heifer #156', time: '6 hours ago', icon: '🩺' },
      { type: 'feeding', animal: 'Herd A', time: '8 hours ago', icon: '🌾' }
    ],
    alerts: [
      { type: 'health', message: 'Cow #134 requires veterinary attention', priority: 'high', icon: '⚠️' },
      { type: 'feeding', message: 'Feed inventory running low', priority: 'medium', icon: '📦' },
      { type: 'breeding', message: '3 animals ready for breeding', priority: 'low', icon: '💝' }
    ],
    performance: {
      milkYield: { current: 1250, target: 1400, unit: 'L/day' },
      weightGain: { current: 1.2, target: 1.5, unit: 'kg/day' },
      feedEfficiency: { current: 85, target: 90, unit: '%' },
      healthScore: { current: 94, target: 95, unit: '%' }
    }
  };

  // BETA modules - Only show the 5 modules available in BETA version
  const betaModules: BetaModule[] = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: <Dashboard />,
      description: `${livestockStats.totalAnimals} Animals • ${livestockStats.healthyAnimals} Healthy • ${livestockStats.milkProduction}L Milk/day`,
      isLocked: false, // Always available in BETA
      isPremium: false,
      color: '#2196F3',
      route: '/beta-dashboard', // Stay on current page for dashboard
      backgroundImage: 'https://wallpaperaccess.com/full/7985150.jpg'
    },
    {
      id: 'animals',
      name: 'Animal Management',
      icon: <Pets />,
      description: `${livestockStats.totalAnimals} Total • ${livestockStats.breeds.length} Breeds • ${livestockStats.newBorns} New Births (Beta: 50 max)`,
      isLocked: false, // Always available in BETA
      isPremium: false,
      color: '#4CAF50',
      route: '/beta-dashboard/animals', // Navigate to animals module
      backgroundImage: 'https://wallpaperaccess.com/full/2973889.jpg'
    },
    {
      id: 'health',
      name: 'Health Monitoring',
      icon: <LocalHospital />,
      description: 'Basic health tracking (Beta Limited)',
      isLocked: false, // Always available in BETA
      isPremium: false,
      color: '#F44336',
      route: '/beta-dashboard/health', // Navigate to health module
      backgroundImage: 'https://wallpaperaccess.com/full/7985153.jpg'
    },
    {
      id: 'resources',
      name: 'Resources & Information',
      icon: <Visibility />,
      description: 'Government resources, auctions, information',
      isLocked: false, // Always available in BETA
      isPremium: false,
      color: '#00BCD4',
      route: '/beta-dashboard/resources', // Navigate to resources module
      backgroundImage: 'https://wallpaperaccess.com/full/6946250.jpg'
    },
    {
      id: 'settings',
      name: 'Settings & Profile',
      icon: <Settings />,
      description: 'Account settings, preferences, and profile management',
      isLocked: false, // Always available in BETA
      isPremium: false,
      color: '#9E9E9E',
      route: '/beta-dashboard/settings', // Navigate to settings module
      backgroundImage: 'https://wallpaperaccess.com/full/7985155.jpg'
    }
    // BETA version only shows 5 modules - premium modules are not displayed
    // Users can upgrade to access: Breeding, Financial, Inventory, Analytics, Compliance, etc.
  ];

  const subscriptionPlans = [
    {
      name: "Professional",
      price: "R299",
      duration: "per month",
      description: "Perfect for growing farms",
      features: [
        "Up to 500 animals",
        "All modules unlocked",
        "Advanced analytics",
        "Priority support",
        "Mobile app access",
        "Data export"
      ],
      color: "#2196F3",
      popular: true,
      savings: "Save 30% vs Beta limits"
    },
    {
      name: "Enterprise",
      price: "R599",
      duration: "per month",
      description: "For large operations",
      features: [
        "Unlimited animals",
        "AI-powered insights",
        "Custom integrations",
        "24/7 support",
        "Multi-farm management",
        "Advanced compliance"
      ],
      color: "#FF9800",
      popular: false,
      savings: "Best value for scale"
    }
  ];

  const handleModuleClick = (module: BetaModule) => {
    if (module.isLocked) {
      setSelectedModule(module);
      setShowUpgradeDialog(true);
    } else if (module.route) {
      // Navigate to the module route
      navigate(module.route);
    } else {
      // Fallback for modules without routes
      console.log(`Opening ${module.name} module`);
      // Show notification for modules without routes
      alert(`${module.name} module is available but route not configured yet.`);
    }
  };

  const handleUpgrade = (planName: string) => {
    navigate('/register', { state: { selectedPlan: planName } });
  };

  const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
    setProfileAnchor(event.currentTarget);
    setShowProfileMenu(true);
  };

  const getTrialStatus = () => {
    if (user.trialDaysLeft <= 3) return 'error';
    if (user.trialDaysLeft <= 7) return 'warning';
    return 'info';
  };

  return (
    <Box sx={{ minHeight: '100vh', position: 'relative' }}>
      {/* Life & Nature Background */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: lifeNatureDesign.gradients.pageBackground,
          zIndex: 0
        }}
      />

      {/* Top Navigation */}
      <AppBar
        position="sticky"
        sx={{
          background: alpha(lifeNatureDesign.colors.primary, 0.95),
          backdropFilter: 'blur(30px)',
          borderBottom: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.3)}`,
          zIndex: 1300,
          boxShadow: `0 4px 20px ${alpha(lifeNatureDesign.colors.primaryDark, 0.3)}`
        }}
      >
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                background: lifeNatureDesign.gradients.primary,
                borderRadius: lifeNatureDesign.shapes.borderRadius.medium,
                p: 1.5,
                mr: 2,
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              <Agriculture sx={{ color: 'white', fontSize: 28 }} />
            </Box>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 900,
                color: lifeNatureDesign.colors.text,
                background: lifeNatureDesign.gradients.life,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}
            >
              AgriIntel
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                ml: 1,
                color: lifeNatureDesign.colors.textAccent,
                fontWeight: 500,
                fontStyle: 'italic'
              }}
            >
              Smart Farming, Smarter Decisions
            </Typography>
            <Chip
              label="BETA"
              size="small"
              sx={{
                ml: 2,
                background: lifeNatureDesign.gradients.harvest,
                color: 'white',
                fontWeight: 'bold',
                borderRadius: lifeNatureDesign.shapes.borderRadius.small
              }}
            />
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <LanguageSelector />
            
            <Badge badgeContent={user.trialDaysLeft} color="error">
              <Timer sx={{ color: theme.palette.text.secondary }} />
            </Badge>
            
            <IconButton onClick={handleProfileClick}>
              <Avatar sx={{ width: 32, height: 32, bgcolor: theme.palette.primary.main }}>
                {user.name.charAt(0)}
              </Avatar>
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Profile Menu */}
      <Menu
        anchorEl={profileAnchor}
        open={showProfileMenu}
        onClose={() => setShowProfileMenu(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            minWidth: 200,
            background: alpha(theme.palette.background.paper, 0.95),
            backdropFilter: 'blur(20px)'
          }
        }}
      >
        <MenuItem onClick={() => navigate('/login')}>
          <Person sx={{ mr: 1 }} /> Profile
        </MenuItem>
        <MenuItem onClick={() => setShowUpgradeDialog(true)}>
          <Upgrade sx={{ mr: 1 }} /> Upgrade
        </MenuItem>
        <MenuItem onClick={() => navigate('/')}>
          <ExitToApp sx={{ mr: 1 }} /> Logout
        </MenuItem>
      </Menu>

      {/* Tabbed Beta Interface */}
      <TabbedBetaInterface
        user={user}
        onUpgrade={() => setShowUpgradeDialog(true)}
        onModuleClick={handleModuleClick}
      />







      {/* Upgrade Dialog */}
      <Dialog
        open={showUpgradeDialog}
        onClose={() => setShowUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            background: 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9))',
            backdropFilter: 'blur(20px)'
          }
        }}
      >
        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            Unlock Premium Features
          </Typography>
          {selectedModule && (
            <Typography variant="body1" color="text.secondary">
              {selectedModule.name} requires a premium subscription
            </Typography>
          )}
        </DialogTitle>

        <DialogContent>
          <Grid container spacing={3}>
            {subscriptionPlans.map((plan) => (
              <Grid item xs={12} md={6} key={plan.name}>
                <Card
                  sx={{
                    position: 'relative',
                    border: plan.popular ? `3px solid ${plan.color}` : '1px solid',
                    borderColor: plan.popular ? plan.color : 'divider',
                    transform: plan.popular ? 'scale(1.02)' : 'scale(1)',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {plan.popular && (
                    <Chip
                      label="Most Popular"
                      sx={{
                        position: 'absolute',
                        top: -12,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        background: `linear-gradient(45deg, ${plan.color}, ${plan.color}80)`,
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  )}

                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {plan.name}
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      <Typography
                        variant="h3"
                        sx={{
                          fontWeight: 'bold',
                          color: plan.color,
                          display: 'inline'
                        }}
                      >
                        {plan.price}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                        {plan.duration}
                      </Typography>
                    </Box>

                    <Chip
                      label={plan.savings}
                      size="small"
                      sx={{
                        background: alpha(plan.color, 0.1),
                        color: plan.color,
                        mb: 2
                      }}
                    />

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      {plan.description}
                    </Typography>

                    <Box sx={{ mb: 3, textAlign: 'left' }}>
                      {plan.features.map((feature, index) => (
                        <Box
                          key={index}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            mb: 1
                          }}
                        >
                          <Star
                            sx={{
                              color: plan.color,
                              fontSize: 16,
                              mr: 1
                            }}
                          />
                          <Typography variant="body2">
                            {feature}
                          </Typography>
                        </Box>
                      ))}
                    </Box>

                    <Button
                      variant={plan.popular ? "contained" : "outlined"}
                      fullWidth
                      size="large"
                      onClick={() => handleUpgrade(plan.name)}
                      sx={{
                        background: plan.popular
                          ? `linear-gradient(45deg, ${plan.color}, ${plan.color}80)`
                          : 'transparent',
                        borderColor: plan.color,
                        color: plan.popular ? 'white' : plan.color,
                        py: 1.5,
                        fontWeight: 'bold',
                        '&:hover': {
                          background: plan.popular
                            ? `linear-gradient(45deg, ${plan.color}90, ${plan.color}70)`
                            : alpha(plan.color, 0.1)
                        }
                      }}
                    >
                      {plan.popular ? 'Start Free Trial' : 'Choose Plan'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          <Box sx={{ mt: 3, p: 2, background: alpha('#4CAF50', 0.1), borderRadius: 2 }}>
            <Typography variant="body2" sx={{ textAlign: 'center', color: '#2E7D32' }}>
              🎉 <strong>Special Beta Offer:</strong> Get 30% off your first 3 months when you upgrade from beta!
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button onClick={() => setShowUpgradeDialog(false)}>
            Continue with Beta
          </Button>
          <Button
            variant="contained"
            onClick={() => navigate('/register')}
            sx={{
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)'
            }}
          >
            View All Plans
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Showcase Dialog */}
      <Dialog
        open={showImageShowcase}
        onClose={() => setShowImageShowcase(false)}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            background: 'linear-gradient(135deg, rgba(10, 14, 39, 0.98), rgba(26, 31, 58, 0.98))',
            backdropFilter: 'blur(30px)',
            border: `1px solid ${alpha('#00D4AA', 0.3)}`,
            maxHeight: '90vh'
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #00D4AA, #7C3AED)',
          color: 'white',
          m: 0
        }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
            🌾 AgriIntel Professional Gallery
          </Typography>
          <IconButton
            onClick={() => setShowImageShowcase(false)}
            sx={{ color: 'white' }}
          >
            <ExitToApp />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ p: 0, overflow: 'hidden' }}>
          <ImageShowcase />
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default EnhancedBeta;
