/**
 * Enhanced Language Selector Component
 * Comprehensive South African language support with visual indicators
 */

import React, { useState } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
  Avatar,
  ListItemIcon,
  ListItemText,
  Divider,
  Tooltip,
  LinearProgress,
  useTheme,
  alpha
} from '@mui/material';
import {
  Language as LanguageIcon,
  Check,
  Star,
  Warning,
  Info
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  southAfricanLanguages, 
  languageGroups, 
  getLanguageStats,
  type Language 
} from '../../locales/southAfricanLanguages';

interface EnhancedLanguageSelectorProps {
  variant?: 'standard' | 'outlined' | 'filled';
  size?: 'small' | 'medium';
  showCompleteness?: boolean;
  showGroups?: boolean;
  showStats?: boolean;
  fullWidth?: boolean;
  disabled?: boolean;
  label?: string;
}

const EnhancedLanguageSelector: React.FC<EnhancedLanguageSelectorProps> = ({
  variant = 'outlined',
  size = 'medium',
  showCompleteness = true,
  showGroups = true,
  showStats = false,
  fullWidth = true,
  disabled = false,
  label = 'Language / Ulimi / Taal'
}) => {
  const theme = useTheme();
  const { language, setLanguage } = useLanguage();
  const [open, setOpen] = useState(false);

  const handleLanguageChange = (event: any) => {
    const selectedLanguage = event.target.value;
    setLanguage(selectedLanguage);
  };

  const getCompletenessColor = (completeness: number) => {
    if (completeness >= 90) return theme.palette.success.main;
    if (completeness >= 75) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const getCompletenessIcon = (completeness: number) => {
    if (completeness >= 90) return <Check fontSize="small" />;
    if (completeness >= 75) return <Warning fontSize="small" />;
    return <Info fontSize="small" />;
  };

  const renderLanguageItem = (lang: Language, isSelected: boolean = false) => (
    <MenuItem 
      key={lang.code} 
      value={lang.code}
      disabled={!lang.enabled}
      sx={{
        py: 1.5,
        px: 2,
        backgroundColor: isSelected ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
        '&:hover': {
          backgroundColor: alpha(theme.palette.primary.main, 0.05),
        },
        '&.Mui-disabled': {
          opacity: 0.5,
        }
      }}
    >
      <ListItemIcon sx={{ minWidth: 40 }}>
        <Avatar 
          sx={{ 
            width: 24, 
            height: 24, 
            fontSize: '0.75rem',
            backgroundColor: 'transparent'
          }}
        >
          {lang.flag}
        </Avatar>
      </ListItemIcon>
      
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: isSelected ? 600 : 400 }}>
              {lang.name}
            </Typography>
            {isSelected && <Check color="primary" fontSize="small" />}
          </Box>
        }
        secondary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
            <Typography variant="caption" color="text.secondary">
              {lang.nativeName}
            </Typography>
            {showCompleteness && (
              <Chip
                size="small"
                icon={getCompletenessIcon(lang.completeness)}
                label={`${lang.completeness}%`}
                sx={{
                  height: 20,
                  fontSize: '0.65rem',
                  backgroundColor: alpha(getCompletenessColor(lang.completeness), 0.1),
                  color: getCompletenessColor(lang.completeness),
                  '& .MuiChip-icon': {
                    fontSize: '0.75rem',
                    color: 'inherit'
                  }
                }}
              />
            )}
          </Box>
        }
      />
    </MenuItem>
  );

  const renderLanguageGroup = (title: string, languages: Language[]) => (
    <Box key={title}>
      <Box sx={{ px: 2, py: 1, backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
        <Typography variant="caption" color="primary" sx={{ fontWeight: 600 }}>
          {title}
        </Typography>
      </Box>
      {languages.map(lang => renderLanguageItem(lang, lang.code === language))}
    </Box>
  );

  const stats = getLanguageStats();

  return (
    <Box>
      <FormControl variant={variant} size={size} fullWidth={fullWidth} disabled={disabled}>
        <InputLabel id="language-selector-label">
          {label}
        </InputLabel>
        <Select
          labelId="language-selector-label"
          value={language}
          onChange={handleLanguageChange}
          open={open}
          onOpen={() => setOpen(true)}
          onClose={() => setOpen(false)}
          label={label}
          startAdornment={
            <LanguageIcon sx={{ mr: 1, color: 'action.active' }} />
          }
          renderValue={(selected) => {
            const selectedLang = southAfricanLanguages.find(lang => lang.code === selected);
            return selectedLang ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <span>{selectedLang.flag}</span>
                <Typography variant="body2">
                  {selectedLang.name}
                </Typography>
                {showCompleteness && (
                  <Chip
                    size="small"
                    label={`${selectedLang.completeness}%`}
                    sx={{
                      height: 20,
                      fontSize: '0.65rem',
                      backgroundColor: alpha(getCompletenessColor(selectedLang.completeness), 0.1),
                      color: getCompletenessColor(selectedLang.completeness)
                    }}
                  />
                )}
              </Box>
            ) : selected;
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                maxHeight: 400,
                width: 320,
                '& .MuiMenuItem-root': {
                  whiteSpace: 'normal',
                }
              }
            }
          }}
        >
          {showStats && (
            <Box sx={{ px: 2, py: 1, backgroundColor: alpha(theme.palette.info.main, 0.05) }}>
              <Typography variant="caption" color="info.main" sx={{ fontWeight: 600 }}>
                Language Support: {stats.enabled}/{stats.total} languages • {stats.avgCompleteness}% avg completion
              </Typography>
            </Box>
          )}

          {showGroups ? (
            <>
              {renderLanguageGroup('Official Languages (Primary)', languageGroups.official.slice(0, 3))}
              <Divider />
              {renderLanguageGroup('Nguni Languages', languageGroups.nguni)}
              <Divider />
              {renderLanguageGroup('Sotho Languages', languageGroups.sotho)}
              <Divider />
              {renderLanguageGroup('Other Official Languages', languageGroups.other)}
              <Divider />
              {renderLanguageGroup('Additional Languages', languageGroups.additional)}
            </>
          ) : (
            southAfricanLanguages
              .filter(lang => lang.enabled)
              .map(lang => renderLanguageItem(lang, lang.code === language))
          )}
        </Select>
      </FormControl>

      {showCompleteness && (
        <Box sx={{ mt: 1 }}>
          <Typography variant="caption" color="text.secondary" gutterBottom>
            Translation Completeness
          </Typography>
          <LinearProgress
            variant="determinate"
            value={southAfricanLanguages.find(lang => lang.code === language)?.completeness || 0}
            sx={{
              height: 4,
              borderRadius: 2,
              backgroundColor: alpha(theme.palette.grey[500], 0.2),
              '& .MuiLinearProgress-bar': {
                backgroundColor: getCompletenessColor(
                  southAfricanLanguages.find(lang => lang.code === language)?.completeness || 0
                ),
                borderRadius: 2,
              }
            }}
          />
        </Box>
      )}
    </Box>
  );
};

// Compact version for headers/toolbars
export const CompactLanguageSelector: React.FC = () => {
  const { language, setLanguage } = useLanguage();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const currentLang = southAfricanLanguages.find(lang => lang.code === language);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageSelect = (langCode: string) => {
    setLanguage(langCode);
    handleClose();
  };

  return (
    <Tooltip title="Select Language">
      <Chip
        avatar={<Avatar sx={{ backgroundColor: 'transparent' }}>{currentLang?.flag}</Avatar>}
        label={currentLang?.name || 'Language'}
        onClick={handleClick}
        variant="outlined"
        size="small"
        sx={{
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: alpha('#000', 0.04),
          }
        }}
      />
    </Tooltip>
  );
};

export default EnhancedLanguageSelector;
