import axios from 'axios';
import { useEffect, useState } from 'react';

// Base API URL - can be configured based on environment
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// Axios instance with common configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle common errors (401, 403, 500, etc.)
    if (error.response) {
      if (error.response.status === 401) {
        // Unauthorized - redirect to login
        localStorage.removeItem('authToken');
        window.location.href = '/login';
      }

      // Log error for debugging
      console.error('API Error:', error.response.status, error.response.data);
    }
    return Promise.reject(error);
  }
);

/**
 * Generic hook for fetching data from the API
 * @param endpoint API endpoint
 * @param params Query parameters
 * @param dependencies Dependencies array for useEffect
 * @returns Object with data, loading state, error, and refetch function
 */
export const useFetchData = <T>(
  endpoint: string,
  params: Record<string, any> = {},
  dependencies: any[] = []
) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await api.get(endpoint, { params });
      setData(response.data);
      setError(null);
    } catch (err) {
      setError(err as Error);
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);

  return { data, loading, error, refetch: fetchData };
};

/**
 * Generic function for creating a new resource
 * @param endpoint API endpoint
 * @param data Data to create
 * @returns Promise with the created resource
 */
export const createResource = async <T>(endpoint: string, data: any): Promise<T> => {
  try {
    const response = await api.post(endpoint, data);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Generic function for updating a resource
 * @param endpoint API endpoint
 * @param id Resource ID
 * @param data Data to update
 * @returns Promise with the updated resource
 */
export const updateResource = async <T>(endpoint: string, id: string, data: any): Promise<T> => {
  try {
    const response = await api.put(`${endpoint}/${id}`, data);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Generic function for deleting a resource
 * @param endpoint API endpoint
 * @param id Resource ID
 * @returns Promise with the deletion result
 */
export const deleteResource = async (endpoint: string, id: string): Promise<void> => {
  try {
    await api.delete(`${endpoint}/${id}`);
  } catch (error) {
    throw error;
  }
};

/**
 * Generic function for batch operations
 * @param endpoint API endpoint
 * @param operation Operation type ('create', 'update', 'delete')
 * @param items Items to process
 * @returns Promise with the batch operation result
 */
export const batchOperation = async <T>(
  endpoint: string,
  operation: 'create' | 'update' | 'delete',
  items: any[]
): Promise<T[]> => {
  try {
    const response = await api.post(`${endpoint}/batch/${operation}`, { items });
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Function to upload files to the server
 * @param endpoint API endpoint
 * @param files Files to upload
 * @param additionalData Additional data to send with the files
 * @returns Promise with the upload result
 */
export const uploadFiles = async <T>(
  endpoint: string,
  files: File[],
  additionalData: Record<string, any> = {}
): Promise<T> => {
  try {
    const formData = new FormData();

    // Append files
    files.forEach((file, index) => {
      formData.append(`file${index}`, file);
    });

    // Append additional data
    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, JSON.stringify(value));
    });

    const response = await api.post(endpoint, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Function to download files from the server
 * @param endpoint API endpoint
 * @param params Query parameters
 * @returns Promise with the download result
 */
export const downloadFile = async (
  endpoint: string,
  params: Record<string, any> = {}
): Promise<Blob> => {
  try {
    const response = await api.get(endpoint, {
      params,
      responseType: 'blob'
    });

    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Function to generate and download reports
 * @param reportType Type of report
 * @param format Format of the report (pdf, excel, csv)
 * @param filters Filters for the report
 * @returns Promise with the report generation result
 */
export const generateReport = async (
  reportType: string,
  format: 'pdf' | 'excel' | 'csv',
  filters: Record<string, any> = {}
): Promise<Blob> => {
  try {
    const response = await api.post(
      '/reports/generate',
      { reportType, format, filters },
      { responseType: 'blob' }
    );

    // Create a download link and trigger it
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${reportType}_${new Date().toISOString().split('T')[0]}.${format}`);
    document.body.appendChild(link);
    link.click();
    link.remove();

    return response.data;
  } catch (error) {
    throw error;
  }
};

export default api;
