// Import API routes
const apiRoutes = require('./api');

module.exports = (app, useMockData = false) => {
  console.log(`Initializing routes with mock data: ${useMockData}`);

  // Mount API routes with /api prefix
  app.use('/api', apiRoutes);

  // Also mount API routes without /api prefix for backward compatibility
  app.use('/', apiRoutes);

  // Simple health check route
  app.get('/health', (req, res) => {
    res.status(200).json({
      success: true,
      status: 'ok',
      message: 'AgriIntel Server is healthy',
      timestamp: new Date(),
      uptime: process.uptime()
    });
  });
};


