/**
 * Add Animal Form Component
 * Example implementation using the Enhanced Form with validation
 */

import React, { useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';
import EnhancedForm, { FormField } from '../forms/EnhancedForm';
import { ValidationRules, commonValidationRules } from '../../utils/formValidationUtils';
import { animalsAPI } from '../../services/api';
import { useSnackbar } from '../../hooks/useSnackbar';

interface AddAnimalFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddAnimalForm: React.FC<AddAnimalFormProps> = ({ open, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { showSnackbar } = useSnackbar();

  // Define form fields
  const formFields: FormField[] = [
    {
      name: 'tagNumber',
      label: 'Tag Number',
      type: 'text',
      required: true,
      placeholder: 'e.g., A001, B-123'
    },
    {
      name: 'name',
      label: 'Animal Name',
      type: 'text',
      required: false,
      placeholder: 'Optional name for the animal'
    },
    {
      name: 'species',
      label: 'Species',
      type: 'select',
      required: true,
      options: [
        { value: 'cattle', label: 'Cattle' },
        { value: 'sheep', label: 'Sheep' },
        { value: 'goat', label: 'Goat' },
        { value: 'pig', label: 'Pig' },
        { value: 'chicken', label: 'Chicken' },
        { value: 'other', label: 'Other' }
      ]
    },
    {
      name: 'breed',
      label: 'Breed',
      type: 'text',
      required: false,
      placeholder: 'e.g., Holstein, Angus, Merino'
    },
    {
      name: 'gender',
      label: 'Gender',
      type: 'select',
      required: true,
      options: [
        { value: 'male', label: 'Male' },
        { value: 'female', label: 'Female' },
        { value: 'unknown', label: 'Unknown' }
      ]
    },
    {
      name: 'birthDate',
      label: 'Birth Date',
      type: 'date',
      required: false
    },
    {
      name: 'acquisitionDate',
      label: 'Acquisition Date',
      type: 'date',
      required: true
    },
    {
      name: 'weight',
      label: 'Weight (kg)',
      type: 'number',
      required: false,
      placeholder: 'Current weight in kilograms'
    },
    {
      name: 'location',
      label: 'Location/Pen',
      type: 'text',
      required: false,
      placeholder: 'e.g., Pen A, Pasture 1, Barn 2'
    },
    {
      name: 'rfidTag',
      label: 'RFID Tag',
      type: 'text',
      required: false,
      placeholder: 'RFID tag number if available'
    },
    {
      name: 'notes',
      label: 'Notes',
      type: 'textarea',
      required: false,
      placeholder: 'Additional notes about the animal',
      rows: 3
    }
  ];

  // Define validation rules
  const validationRules: ValidationRules = {
    tagNumber: {
      required: true,
      pattern: /^[A-Z0-9-]+$/,
      custom: (value: string) => {
        if (value && !/^[A-Z0-9-]+$/.test(value)) {
          return 'Tag number can only contain uppercase letters, numbers, and hyphens';
        }
        if (value && value.length < 2) {
          return 'Tag number must be at least 2 characters long';
        }
        return null;
      }
    },
    name: {
      ...commonValidationRules.name,
      required: false
    },
    species: {
      required: true
    },
    gender: {
      required: true
    },
    acquisitionDate: {
      required: true,
      custom: (value: any) => {
        if (value && isNaN(Date.parse(value))) {
          return 'Please enter a valid date';
        }
        if (value && new Date(value) > new Date()) {
          return 'Acquisition date cannot be in the future';
        }
        return null;
      }
    },
    birthDate: {
      custom: (value: any) => {
        if (value && isNaN(Date.parse(value))) {
          return 'Please enter a valid date';
        }
        if (value && new Date(value) > new Date()) {
          return 'Birth date cannot be in the future';
        }
        return null;
      }
    },
    weight: {
      custom: (value: any) => {
        if (value) {
          const num = parseFloat(value);
          if (isNaN(num)) {
            return 'Weight must be a valid number';
          }
          if (num <= 0) {
            return 'Weight must be greater than 0';
          }
          if (num > 2000) {
            return 'Weight seems unrealistic (max 2000kg)';
          }
        }
        return null;
      }
    },
    rfidTag: {
      pattern: /^[A-Z0-9]+$/,
      custom: (value: string) => {
        if (value && !/^[A-Z0-9]+$/.test(value)) {
          return 'RFID tag can only contain uppercase letters and numbers';
        }
        return null;
      }
    }
  };

  // Initial form values
  const initialValues = {
    tagNumber: '',
    name: '',
    species: '',
    breed: '',
    gender: '',
    birthDate: '',
    acquisitionDate: new Date().toISOString().split('T')[0], // Today's date
    weight: '',
    location: '',
    rfidTag: '',
    notes: ''
  };

  // Handle form submission
  const handleSubmit = async (values: Record<string, any>) => {
    setLoading(true);
    setError(null);

    try {
      // Prepare data for API
      const animalData = {
        ...values,
        weight: values.weight ? parseFloat(values.weight) : null,
        birthDate: values.birthDate ? new Date(values.birthDate) : null,
        acquisitionDate: new Date(values.acquisitionDate),
        status: 'active',
        healthStatus: 'unknown'
      };

      // Submit to API
      await animalsAPI.createAnimal(animalData);
      
      showSnackbar('Animal added successfully!', 'success');
      onSuccess();
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add animal';
      setError(errorMessage);
      showSnackbar(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle>Add New Animal</DialogTitle>
      <DialogContent>
        <EnhancedForm
          fields={formFields}
          initialValues={initialValues}
          validationRules={validationRules}
          onSubmit={handleSubmit}
          onCancel={onClose}
          submitButtonText="Add Animal"
          loading={loading}
          error={error}
        />
      </DialogContent>
    </Dialog>
  );
};

export default AddAnimalForm;
