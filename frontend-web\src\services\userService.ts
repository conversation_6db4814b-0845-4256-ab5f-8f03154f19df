import axios from 'axios';

// Define user types
export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'manager' | 'super_user' | 'staff' | 'veterinarian' | 'viewer';
  status: 'active' | 'inactive' | 'suspended';
  permissions: any; // Can be array of strings or object with permissions
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
  phoneNumber?: string;
  position?: string;
  department?: string;
  profileImage?: string;
}

export interface LoginResponse {
  user: User;
  token: string;
}

// Base API URL - would come from environment in production
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// User service with MongoDB integration
export const userService = {
  // Login user
  login: async (username: string, password: string): Promise<LoginResponse> => {
    try {
      console.log('Attempting login with API URL:', API_URL);

      // Try to call the API to authenticate
      try {
        const response = await axios.post(`${API_URL}/auth/login`, { username, password });

        // Format the response data
        const userData = response.data.user;
        const token = response.data.token;

        console.log('API login successful');
        return {
          user: userData,
          token
        };
      } catch (apiError) {
        console.error('API login failed:', apiError);
        console.log('Using mock data for login');

        // Mock login for testing
        if (username === 'admin' && password === 'Admin@123') {
          const mockUser = {
            id: '1',
            username: 'admin',
            email: '<EMAIL>',
            firstName: 'Admin',
            lastName: 'User',
            role: 'admin',
            status: 'active',
            permissions: ['all'],
            lastLogin: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          };

          const mockToken = 'mock-jwt-token-for-testing';

          return {
            user: mockUser,
            token: mockToken
          };
        }

        // If no authentication method worked, throw an error
        throw new Error('Invalid credentials. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      if (axios.isAxiosError(error) && error.response) {
        // Use the error message from the server if available
        throw new Error(error.response.data.error || 'Invalid credentials. Please try again.');
      }
      throw new Error('Invalid credentials. Please try again.');
    }
  },

  // Get current user
  getCurrentUser: async (): Promise<User | null> => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return null;

      console.log('Fetching current user from API');

      try {
        // Call the API to get current user
        const response = await axios.get(`${API_URL}/auth/me`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        console.log('Current user fetched successfully:', response.data);
        return response.data;
      } catch (apiError) {
        console.error('API getCurrentUser failed:', apiError);
        console.log('Using mock data for getCurrentUser');

        // Check if token is the mock token
        if (token === 'mock-jwt-token-for-testing') {
          const mockUser = {
            id: '1',
            username: 'admin',
            email: '<EMAIL>',
            firstName: 'Admin',
            lastName: 'User',
            role: 'admin',
            status: 'active',
            permissions: ['all'],
            lastLogin: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          };

          return mockUser;
        }

        throw apiError;
      }
    } catch (error) {
      console.error('Get current user error:', error);
      // Clear token and user data if authentication fails
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      return null;
    }
  },

  // Register new user (admin only)
  registerUser: async (userData: Partial<User>): Promise<User> => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Call the API to register user
      const response = await axios.post(`${API_URL}/users`, userData, {
        headers: { Authorization: `Bearer ${token}` }
      });

      return response.data;
    } catch (error) {
      console.error('Register user error:', error);
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(error.response.data.error || 'Failed to register user. Please try again.');
      }
      throw new Error('Failed to register user. Please try again.');
    }
  },

  // Get all users (admin only)
  getAllUsers: async (): Promise<User[]> => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Call the API to get all users
      const response = await axios.get(`${API_URL}/users`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      return response.data;
    } catch (error) {
      console.error('Get all users error:', error);
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(error.response.data.error || 'Failed to fetch users. Please try again.');
      }
      throw new Error('Failed to fetch users. Please try again.');
    }
  },

  // Update user (admin or self)
  updateUser: async (userId: string, userData: Partial<User>): Promise<User> => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Call the API to update user
      const response = await axios.put(`${API_URL}/users/${userId}`, userData, {
        headers: { Authorization: `Bearer ${token}` }
      });

      return response.data;
    } catch (error) {
      console.error('Update user error:', error);
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(error.response.data.error || 'Failed to update user. Please try again.');
      }
      throw new Error('Failed to update user. Please try again.');
    }
  },

  // Delete user (admin only)
  deleteUser: async (userId: string): Promise<void> => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Call the API to delete user
      await axios.delete(`${API_URL}/users/${userId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
    } catch (error) {
      console.error('Delete user error:', error);
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(error.response.data.error || 'Failed to delete user. Please try again.');
      }
      throw new Error('Failed to delete user. Please try again.');
    }
  }
};
